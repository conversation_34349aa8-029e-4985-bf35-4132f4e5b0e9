import React, { memo, useEffect, useState } from 'react';
import { Controller } from 'react-hook-form';

import AppButton from '../../../components/Common/AppButton';
import TextInput from '../../../components/Common/MUIInput';
import SelectInput from '../../../components/Common/MUISelect';
import { useRoles } from '../../roles/hooks/useRoles';
import { useUserForm } from '../hooks/useUserForm';
import { UserFormSchema } from '../schemas/user.schema';
import { User, USER_STATUSES } from '../types/user.types';

interface UserFormProps {
  user?: User | null;
  organizationId?: string | null | undefined;
  onSubmit: (data: UserFormSchema) => void | Promise<void>;
  onSuccess: () => void;
  onCancel: () => void;
}

const UserForm: React.FC<UserFormProps> = memo(
  ({ user, organizationId, onSubmit, onSuccess, onCancel }) => {
    const {
      roles,
      fetchRoles,
      loading: rolesLoading,
    } = useRoles(organizationId || undefined);
    const {
      control,
      handleSubmit: formSubmitHandler,
      errors,
      isSubmitting,
      isEditing,
      setValue,
      watch,
    } = useUserForm({
      user: user || null,
      onSubmit,
      onSuccess,
    });

    const selectedRole = watch('userRole');
    const [submitAttempted, setSubmitAttempted] = useState(false);
    // Update roleId when role selection changes
    useEffect(() => {
      if (selectedRole && roles.length > 0) {
        const role = roles.find((r) => r.name === selectedRole);
        if (role) {
          setValue('roleId', role.id);
        }
      }
    }, [selectedRole, roles, setValue]);

    // Only fetch roles if we have an organization ID
    useEffect(() => {
      if (organizationId) {
        fetchRoles();
      }
    }, [fetchRoles, organizationId]);

    // Reset form when user changes or when opening the form
    useEffect(() => {
      if (!isEditing) {
        // For new user, reset role fields immediately
        setValue('userRole', '');
        setValue('roleId', '');
      } else if (user) {
        // For existing user, set the role from user data
        setValue('userRole', user.userRole || '');
        setValue('roleId', user.roleId || '');
      }
    }, [user, isEditing, setValue]);

    // Update roleId when role selection changes
    useEffect(() => {
      if (selectedRole && roles.length > 0) {
        const role = roles.find((r) => r.name === selectedRole);
        if (role) {
          setValue('roleId', role.id);
        }
      } else if (!selectedRole) {
        setValue('roleId', '');
      }
    }, [selectedRole, roles, setValue]);

    // Update role selection when roles are loaded for existing user
    useEffect(() => {
      if (isEditing && user?.userRole && roles.length > 0 && !rolesLoading) {
        const matchingRole = roles.find(
          (role) => role.name.toLowerCase() === user.userRole?.toLowerCase()
        );
        if (matchingRole) {
          setValue('userRole', matchingRole.name);
          setValue('roleId', matchingRole.id);
        }
      }
    }, [isEditing, user, roles, rolesLoading, setValue]);

    return (
      <div className='space-y-6 p-6'>
        <form
          onSubmit={(e) => {
            setSubmitAttempted(true);
            formSubmitHandler(e);
          }}
          className='space-y-4'
        >
          {/* Name Field */}
          <div>
            <Controller
              name='name'
              control={control}
              render={({ field }) => (
                <TextInput
                  {...field}
                  label='Name *'
                  error={submitAttempted && !!errors.name}
                  helperText={submitAttempted ? errors.name?.message : ''}
                  placeholder='Enter name'
                />
              )}
            />
          </div>
          <div>
            <Controller
              name='email'
              control={control}
              render={({ field }) => (
                <TextInput
                  {...field}
                  label='Email Address *'
                  error={submitAttempted && !!errors.email}
                  helperText={submitAttempted ? errors.email?.message : ''}
                  placeholder='Enter email address'
                  type='email'
                />
              )}
            />
          </div>

          {/* User Role */}
          <div>
            <Controller
              name='userRole'
              control={control}
              render={({ field }) => (
                <SelectInput
                  {...field}
                  label={rolesLoading ? '' : 'User Role *'}
                  error={submitAttempted && !!errors.userRole}
                  helperText={submitAttempted ? errors.userRole?.message : ''}
                  disabled={rolesLoading}
                  options={
                    rolesLoading
                      ? // When loading, show user's current role if editing, otherwise show loading
                        user && user.userRole
                        ? [
                            {
                              label: `${user.userRole} (Current)`,
                              value: user.userRole,
                            },
                          ]
                        : [{ label: 'Loading roles...', value: '' }]
                      : (() => {
                          // Create options from available roles
                          const roleOptions = roles.map((role) => ({
                            label: role.name,
                            value: role.name,
                          }));

                          // If editing a user and their role is not in the available roles,
                          // add it as an option so it can be displayed and selected
                          if (user && user.userRole) {
                            const hasMatchingRole = roles.some(
                              (role) =>
                                role.name.toLowerCase() ===
                                  user.userRole?.toLowerCase() ||
                                role.name.toLowerCase().replace(/\s+/g, '_') ===
                                  user.userRole?.toLowerCase() ||
                                role.name.toLowerCase().replace(/_/g, ' ') ===
                                  user.userRole?.toLowerCase()
                            );

                            if (!hasMatchingRole) {
                              // Add the user's current role as an option
                              roleOptions.unshift({
                                label: `${user.userRole} (Current)`,
                                value: user.userRole,
                              });
                            }
                          }

                          return roleOptions;
                        })()
                  }
                />
              )}
            />
          </div>

          {/* Status - Only enabled in edit mode */}
          <div>
            <Controller
              name='status'
              control={control}
              render={({ field }) => (
                <SelectInput
                  {...field}
                  label='Status *'
                  error={submitAttempted && !!errors.status}
                  helperText={submitAttempted ? errors.status?.message : ''}
                  disabled={!isEditing}
                  options={USER_STATUSES.map((status) => ({
                    label: status.label,
                    value: status.value,
                  }))}
                  sx={!isEditing ? { backgroundColor: '#f3f4f6' } : {}}
                />
              )}
            />
          </div>

          {/* Form Actions */}
          <div className='flex justify-end space-x-4 pt-4'>
            <AppButton
              type='button'
              kind='secondary'
              onClick={onCancel}
              disabled={isSubmitting}
            >
              Cancel
            </AppButton>
            <AppButton type='submit' kind='primary'>
              {isSubmitting
                ? 'Saving...'
                : isEditing
                  ? 'Save Changes'
                  : 'Create User'}
            </AppButton>
          </div>
        </form>
      </div>
    );
  }
);

UserForm.displayName = 'UserForm';

export default UserForm;
