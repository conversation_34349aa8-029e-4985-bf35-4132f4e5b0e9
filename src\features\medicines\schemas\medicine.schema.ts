import * as yup from 'yup';

// Medicine filter form schema
export const medicineFilterSchema = yup.object({
  searchText: yup.string().trim(),
  isActive: yup.string().oneOf(['all', 'active', 'inactive']),
});

export type MedicineFilterSchema = yup.InferType<typeof medicineFilterSchema>;

// Bulk update form schema
export const bulkUpdateSchema = yup.object({
  selectedMedicines: yup
    .array()
    .of(yup.string().required())
    .min(1, 'Please select at least one medicine'),
  action: yup
    .string()
    .oneOf(['activate', 'deactivate', 'updatePrice'])
    .required(),
  price: yup
    .number()
    .typeError('Please enter a valid number')
    .positive('Price must be greater than 0')
    .when('action', {
      is: 'updatePrice',
      then: (schema) => schema.required('Price is required'),
      otherwise: (schema) => schema.optional(),
    }),
});

export type BulkUpdateSchema = yup.InferType<typeof bulkUpdateSchema>;

// Individual medicine update schema
export const medicineUpdateSchema = yup.object().shape({
  medicineId: yup.string().required('Medicine ID is required'),
  isActive: yup.boolean().required('Status is required'),
  price: yup
    .number()
    .typeError('Please enter a valid number')
    .positive('Price must be greater than 0')
    .required('Price is required'),
});

export type MedicineUpdateSchema = yup.InferType<typeof medicineUpdateSchema>;

// Price update schema for inline editing
export const priceUpdateSchema = yup.object({
  price: yup
    .number()
    .typeError('Please enter a valid number')
    .positive('Price must be greater than 0')
    .max(999999, 'Price is too high')
    .required('Price is required'),
});

export type PriceUpdateSchema = yup.InferType<typeof priceUpdateSchema>;
