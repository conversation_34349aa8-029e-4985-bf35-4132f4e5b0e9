import { Check<PERSON>ir<PERSON>, Close, Error } from '@mui/icons-material';
import {
  Box,
  CircularProgress,
  IconButton,
  LinearProgress,
  Paper,
  Typography,
} from '@mui/material';
import React from 'react';

import type { BulkUpdateStatus as BulkUpdateStatusType } from '../types/bulk-update.types';

interface BulkUpdateStatusProps {
  status: BulkUpdateStatusType | null;
  isPolling: boolean;
  onClose: () => void;
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'COMPLETED':
      return 'success.main';
    case 'FAILED':
      return 'error.main';
    case 'PROCESSING':
    case 'PENDING':
      return 'primary.main';
    default:
      return 'text.primary';
  }
};

const formatTime = (dateString: string | null) => {
  if (!dateString) return '--:--:--';
  return new Date(dateString).toLocaleTimeString();
};

export const BulkUpdateStatus: React.FC<BulkUpdateStatusProps> = ({
  status,
  isPolling,
  onClose,
}) => {
  if (!status || !isPolling) return null;

  const progress = status.progress || 0;
  const showProgress = ['PROCESSING', 'PENDING'].includes(status.status);
  const showCloseButton =
    status.status !== 'PROCESSING' && status.status !== 'PENDING';
  // Processed percentage is calculated but not used in the component
  // const processedPercentage = status.totalItems > 0
  //   ? Math.round((status.processedItems / status.totalItems) * 100)
  //   : 0;

  return (
    <Paper
      elevation={3}
      sx={{
        position: 'fixed',
        bottom: 20,
        right: 20,
        width: 400,
        p: 2,
        zIndex: 9999,
        borderLeft: `4px solid`,
        borderColor: getStatusColor(status.status),
      }}
    >
      <Box display='flex' alignItems='flex-start' mb={showProgress ? 1 : 0}>
        <Box flexGrow={1}>
          <Box display='flex' alignItems='center' mb={1}>
            {status.status === 'COMPLETED' ? (
              <CheckCircle color='success' fontSize='small' sx={{ mr: 1 }} />
            ) : status.status === 'FAILED' ? (
              <Error color='error' fontSize='small' sx={{ mr: 1 }} />
            ) : (
              <CircularProgress size={16} sx={{ mr: 1 }} />
            )}
            <Typography variant='subtitle2' fontWeight='medium'>
              {status.type === 'LOINC_UPDATE' ? 'Bulk Update' : 'Bulk Remove'}
            </Typography>
          </Box>

          {status.message && (
            <Typography variant='body2' color='text.secondary' mb={1}>
              {status.message}
            </Typography>
          )}

          {showProgress && (
            <Box mt={1.5}>
              <Box display='flex' justifyContent='space-between' mb={0.5}>
                <Typography variant='caption' color='text.secondary'>
                  Progress: {Math.round(progress)}%
                </Typography>
                <Typography variant='caption' fontWeight='medium'>
                  {status.processedItems.toLocaleString()} of{' '}
                  {status.totalItems.toLocaleString()} items
                </Typography>
              </Box>
              <LinearProgress
                variant='determinate'
                value={progress}
                sx={{
                  height: 8,
                  borderRadius: 4,
                  '& .MuiLinearProgress-bar': {
                    borderRadius: 4,
                    backgroundColor: getStatusColor(status.status),
                  },
                }}
              />
              <Box display='flex' justifyContent='space-between' mt={1}>
                <Typography variant='caption' color='text.secondary'>
                  Started: {formatTime(status.startTime)}
                </Typography>
                <Typography variant='caption' color='text.secondary'>
                  {status.endTime
                    ? `Completed: ${formatTime(status.endTime)}`
                    : 'In progress...'}
                </Typography>
              </Box>
            </Box>
          )}
        </Box>

        {showCloseButton && (
          <IconButton
            size='small'
            onClick={onClose}
            sx={{ ml: 1, mt: -1, mr: -1 }}
            aria-label='Close status'
          >
            <Close fontSize='small' />
          </IconButton>
        )}
      </Box>

      {status.status === 'FAILED' && status.error && (
        <Typography variant='caption' color='error' mt={1} display='block'>
          Error: {status.error}
        </Typography>
      )}
    </Paper>
  );
};
