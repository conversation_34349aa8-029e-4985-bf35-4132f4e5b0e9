import { Action, Middleware } from '@reduxjs/toolkit';

import { clearSuccessMessage } from '../rootActions';

// Global toast handler - will be set by the ToastProvider
let globalToastHandler: {
  success: (title: string, message?: string) => void;
  error: (title: string, message?: string) => void;
  warning: (title: string, message?: string) => void;
  info: (title: string, message?: string) => void;
} | null = null;

// Track the last shown message to prevent duplicates
let lastMessage: { key: string; timestamp: number } | null = null;
const MESSAGE_DEBOUNCE = 3000; // 3 seconds

export const setGlobalToastHandler = (handler: typeof globalToastHandler) => {
  globalToastHandler = handler;
};

// Enhanced function to check if an error is from a cancelled request
const isCancelledRequestError = (error: any): boolean => {
  if (!error) return false;

  // Check for our custom RequestCancelledError
  if (error?.name === 'RequestCancelledError') {
    return true;
  }

  // Check for axios cancel token cancellation
  if (error?.name === 'CanceledError' || error?.name === 'Cancel') {
    return true;
  }

  // Check message content (case-insensitive)
  const message = error?.message || error || '';
  if (typeof message === 'string') {
    const lowerMessage = message.toLowerCase();
    if (
      lowerMessage.includes('cancel') ||
      lowerMessage.includes('abort') ||
      lowerMessage.includes('request was cancelled') ||
      lowerMessage.includes('operation was cancelled')
    ) {
      return true;
    }
  }

  // Check error code/status
  if (
    error?.code === 'ERR_CANCELED' ||
    error?.code === 'CANCELLED' ||
    error?.status === 'cancelled' ||
    error?.status === 'canceled'
  ) {
    return true;
  }

  // Check if axios.isCancel would return true (for axios errors)
  if (error?.__CANCEL__ === true || error?.[Symbol.toStringTag] === 'Cancel') {
    return true;
  }

  return false;
};

export const toastMiddleware: Middleware =
  (store) => (next) => (action: unknown) => {
    // First let the action update the state
    const result = next(action);

    if (!globalToastHandler || !isAction(action)) {
      return result;
    }

    // Only process fulfilled/rejected actions
    if (
      !action.type.endsWith('/fulfilled') &&
      !action.type.endsWith('/rejected')
    ) {
      return result;
    }

    const state = store.getState();
    const actionSlice = action.type.split('/')[0];

    if (!actionSlice || !state[actionSlice as keyof typeof state]) {
      return result;
    }

    const sliceState = state[actionSlice as keyof typeof state];
    const now = Date.now();

    // Handle success messages
    if (sliceState?.successMessage) {
      const messageKey = `success-${sliceState.successMessage}`;

      // Only show if this is a new message or the same message after debounce period
      if (
        !lastMessage ||
        lastMessage.key !== messageKey ||
        now - lastMessage.timestamp > MESSAGE_DEBOUNCE
      ) {
        // Clear the message from state first
        store.dispatch(clearSuccessMessage({ slice: actionSlice }));

        // Show the toast
        globalToastHandler.success('Success', sliceState.successMessage);

        // Update last message
        lastMessage = { key: messageKey, timestamp: now };
      }
    }
    // Handle error messages
    else if (sliceState?.error) {
      const error = sliceState.error;

      // Skip showing toast for cancelled requests
      if (isCancelledRequestError(error)) {
        console.log('Skipping toast for cancelled request:', error); // Debug log
        return result;
      }

      // Only show non-cancellation errors
      const messageKey = `error-${error}`;
      if (
        !lastMessage ||
        lastMessage.key !== messageKey ||
        now - lastMessage.timestamp > MESSAGE_DEBOUNCE
      ) {
        globalToastHandler.error('Error', error);
        lastMessage = { key: messageKey, timestamp: now };
      }
    }
    // Handle errorMessage (used by some slices)
    else if (sliceState?.errorMessage) {
      const errorMessage = sliceState.errorMessage;

      // Skip showing toast for cancelled requests
      if (isCancelledRequestError(errorMessage)) {
        console.log('Skipping toast for cancelled request:', errorMessage); // Debug log
        return result;
      }

      // Only show non-cancellation errors
      const messageKey = `error-${errorMessage}`;
      if (
        !lastMessage ||
        lastMessage.key !== messageKey ||
        now - lastMessage.timestamp > MESSAGE_DEBOUNCE
      ) {
        globalToastHandler.error('Error', errorMessage);
        lastMessage = { key: messageKey, timestamp: now };
      }
    }

    return result;
  };

// Type guard for action
function isAction(action: unknown): action is Action {
  return (action as Action).type !== undefined;
}

// Helper function to show custom toasts from components
export const showToast = (
  type: 'success' | 'error' | 'warning' | 'info',
  title: string,
  message?: string
) => {
  if (!globalToastHandler) {
    console.warn('Toast handler not initialized');
    return;
  }

  const messageKey = `${type}-${title}-${message || ''}`;
  const now = Date.now();

  // Only show if this is a new message or the same message after debounce period
  if (
    !lastMessage ||
    lastMessage.key !== messageKey ||
    now - lastMessage.timestamp > MESSAGE_DEBOUNCE
  ) {
    globalToastHandler[type](title, message);
    lastMessage = { key: messageKey, timestamp: now };
  }
};
