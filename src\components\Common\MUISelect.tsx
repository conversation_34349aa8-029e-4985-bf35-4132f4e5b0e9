import MenuItem from '@mui/material/MenuItem';
import TextField, { TextFieldProps } from '@mui/material/TextField';
import React from 'react';

interface Option {
  label: string;
  value: string;
}

type SelectInputProps = {
  label?: string;
  options: Option[];
} & Omit<TextFieldProps, 'select'>;

const SelectInput: React.FC<SelectInputProps> = ({
  label,
  options,
  ...props
}) => (
  <TextField
    select
    fullWidth
    size='small'
    label={label}
    variant='outlined'
    {...props}
    SelectProps={{
      displayEmpty: true,
      ...props.SelectProps,
    }}
  >
    {options.map((option) => (
      <MenuItem key={option.value} value={option.value}>
        {option.label}
      </MenuItem>
    ))}
  </TextField>
);

export default SelectInput;
