// Core entity types
export interface User {
  id: string;
  firstName: string;
  lastName: string;
  name: string; // API uses single name field
  email: string;
  phone?: string;
  roles: Role[];
  userRole: string; // API uses userRole field
  status: 'active' | 'inactive';
  organizationId: string;
  organization?: Organization;
  lastLogin?: Date;
  createdAt: Date;
  mustResetPassword: boolean;
  organizationName?: string;
}

export interface Organization {
  id: string;
  name: string;
  address: {
    street: string; // changed from street1
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  contactPersonName: string; // changed from contactPerson
  contactEmail: string;
  contactPhone?: string;
  description?: string;
  isActive: boolean; // changed from status
  status: 'active' | 'inactive'; // Add this line
  createdAt: string;
  updatedAt: string;
  created_by: string;
  updated_by: string;
  created_on: string;
  updated_on: string;
  _rid?: string;
  _self?: string;
  _etag?: string;
  _attachments?: string;
  _ts?: number;
}

export interface Role {
  id: string;
  name: string;
  description?: string;
  permissions: Permission[];
  organizationId: string;
  departmentId?: string;
  isSystem: boolean; // Keep for backward compatibility
  isDefault?: boolean; // Add isDefault field from API
  createdAt: Date;
}

export interface Permission {
  id: string;
  key?: string; // Permission key from backend API
  api: string;
  methods: string[];
  // Extended properties for UI management
  module?: 'EMR' | 'MRD' | 'ADMIN';
  feature?: string;
  actions?: ('create' | 'read' | 'update' | 'delete' | 'print' | 'export')[];
}

export type ModuleType = 'EMR' | 'MRD' | 'ADMIN';
export type PermissionAction =
  | 'create'
  | 'read'
  | 'update'
  | 'delete'
  | 'print'
  | 'export';

export interface Department {
  id: string;
  name: string;
  description?: string;
  status: 'active' | 'inactive';
  organizationId: string;
  createdAt: Date;
}

export interface Branch {
  id: string;
  name: string;
  address: string;
  contactPhone: string;
  status: 'active' | 'inactive';
  organizationId: string;
  createdAt: Date;
}

export interface Bank {
  id: string;
  bankName: string;
  accountNumber: string;
  ifscCode: string;
  branchName: string;
  accountHolderName: string;
  status: 'active' | 'inactive';
  organizationId: string;
  createdAt: Date;
}

export interface Language {
  id: string;
  name: string;
  isoCode: string;
  status: 'active' | 'inactive';
  organizationId: string;
  createdAt: Date;
}

export interface VitalType {
  id: string;
  name: string;
  unit: string;
  dataType: 'numeric' | 'numeric-range' | 'text' | 'boolean';
  status: 'active' | 'inactive';
  organizationId: string;
  createdAt: Date;
}

export interface Patient {
  id: string;
  firstName: string;
  lastName: string;
  fullName: string; // Full name from API
  age: number;
  dob?: string; // Date of birth in YYYY-MM-DD format
  gender: 'Male' | 'Female' | 'Other'; // Capitalized to match API
  contactPhone?: string;
  email?: string;
  organizationId: string;
  registrationDate: Date;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface Doctor {
  id: string;
  firstName: string;
  lastName: string;
  specialty: string;
  designation: string;
  email: string;
  contactPhone?: string;
  departmentId: string;
  branchIds: string[];
  status: 'active' | 'inactive';
  organizationId: string;
  createdAt: Date;
}

export interface AuditLog {
  id: string;
  timestamp: Date;
  userId: string;
  userName: string;
  actionType: string;
  entityType: string;
  entityId: string;
  details: string;
  ipAddress: string;

  organizationId: string;
}

export interface Template {
  id: string;
  name: string;
  type: string; // e.g., 'Prescription', 'Discharge Summary', 'Lab Report'
  content: string; // HTML content for the template
  status: 'active' | 'inactive';
  organizationId: string;
  createdAt: Date;
}

// Form types
export interface CreateUserForm {
  firstName: string;
  lastName: string;
  name: string; // API uses single name field
  email: string;
  phone?: string;
  roleIds: string[];
  userRole: string; // API uses userRole field
  status: 'active' | 'inactive';
  organizationId: string; // API requires organizationId
}

export interface CreateOrganizationForm {
  name: string;
  contactPersonName: string;
  contactEmail: string;
  contactPhone?: string;
  address: {
    street1: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  description?: string;
}

// API Response types
export interface ApiResponse<T> {
  data: T;
  message: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
}

// Filter and search types
export interface TableFilters {
  search?: string;
  status?: string;
  dateFrom?: Date;
  dateTo?: Date;
}

export interface SortConfig {
  field: string;
  direction: 'asc' | 'desc';
}

// Auth types
export interface AuthState {
  user: User | null;
  selectedOrganization: Organization | null;
  currentOrganization: Organization | null;
  isAuthenticated: boolean;
  isSuperAdmin: boolean;
  isOrganizationAdmin: boolean;
  isOrganizationUser: boolean;
  loading: boolean;
  error: string | null;
  successMessage: string | null;
  loggingOut: boolean;
  emrUserInfo: {
    id: string;
    email: string;
    name: string;
    [key: string]: any;
  } | null;
}

export type UserRole =
  | 'SUPER_ADMIN'
  | 'ORGANIZATION_ADMIN'
  | 'DOCTOR'
  | 'NURSE'
  | 'RECEPTIONIST';
