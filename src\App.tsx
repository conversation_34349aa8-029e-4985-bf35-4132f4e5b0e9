import 'react-toastify/dist/ReactToastify.css';

import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@azure/msal-react';
import React, { useEffect } from 'react';
import { Provider } from 'react-redux';
import { ToastContainer } from 'react-toastify';

import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from './components/Auth/MsalAuthHandler';
// Components
import URLRedirectHandler from './components/Common/URLRedirectHandler';
import { ToastProvider, useToast } from './contexts/ToastContext';
import RoutesComponent from './RoutesComponent';
import { store } from './store';
import { setGlobalToastHandler } from './store/middleware/toastMiddleware';
// Utils & Config
import { msalInstance } from './utils/msal';

const AppContent: React.FC = () => {
  const { success, error } = useToast();

  // Set up the global toast handler
  useEffect(() => {
    const toastHandler = {
      success: (title: string, message?: string) => {
        success(message || title);
      },
      error: (title: string, message?: string) => {
        error(message || title);
      },
      info: (title: string, message?: string) => {
        // Implement if needed
        console.info(`[INFO] ${title}${message ? `: ${message}` : ''}`);
      },
      warning: (title: string, message?: string) => {
        // Implement if needed
        console.warn(`[WARNING] ${title}${message ? `: ${message}` : ''}`);
      },
    };

    setGlobalToastHandler(toastHandler);

    // Clean up the global handler when the component unmounts
    return () => {
      setGlobalToastHandler(null);
    };
  }, [success, error]);

  return (
    <div className='app-container'>
      <URLRedirectHandler>
        <ToastProvider>
          <RoutesComponent />
        </ToastProvider>
      </URLRedirectHandler>
    </div>
  );
};

// Simple ErrorBoundary component
class ErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(_error: Error) {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className='flex h-screen flex-col items-center justify-center bg-gray-100 p-4'>
          <h1 className='mb-4 text-2xl font-bold text-red-600'>
            Something went wrong
          </h1>
          <p className='mb-4 text-gray-700'>
            Please refresh the page or try again later.
          </p>
          <button
            className='rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600'
            onClick={() => window.location.reload()}
          >
            Refresh Page
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

const App: React.FC = () => {
  return (
    <ErrorBoundary>
      <Provider store={store}>
        <MsalProvider instance={msalInstance}>
          <ToastProvider>
            <MsalAuthHandler>
              <URLRedirectHandler>
                <AppContent />
                <ToastContainer
                  position='top-right'
                  autoClose={5000}
                  hideProgressBar={false}
                  newestOnTop={false}
                  closeOnClick
                  rtl={false}
                  pauseOnFocusLoss
                  draggable
                  pauseOnHover
                  theme='light'
                />
              </URLRedirectHandler>
            </MsalAuthHandler>
          </ToastProvider>
        </MsalProvider>
      </Provider>
    </ErrorBoundary>
  );
};

export default App;
