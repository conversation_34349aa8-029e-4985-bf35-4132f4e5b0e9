import { ROLES_ENDPOINT } from '../../../constants/api-endpoints';
import api from '../../../services/api';

const apiUrl = ROLES_ENDPOINT;
const pagination = { size: 10 };

export type RoleParams = {
  organizationId?: string; // Optional for super admin without organization selection
  searchText?: string; // For search functionality (changed from search/name to searchText)
  page?: number;
  pageSize?: number;
};

export type CreateRoleData = {
  name: string;
  description?: string;
  organizationId: string;
};

export type UpdateRoleData = {
  id: string;
  name?: string;
  description?: string;
  organizationId: string;
};

export type RoleResponse = {
  id: string;
  name: string;
  description?: string; // Make description optional since it can be empty
  organizationId: string;
  isDefault?: boolean; // Add isDefault field from API
};

export type RolesListResponse = {
  roles?: RoleResponse[]; // Make it optional in case API returns direct array
  totalRecords?: number;
  totalPages?: number;
  currentPage?: number;
};

const fetchRoles = async (params: RoleParams) => {
  const {
    organizationId,
    searchText = '',
    page = 1,
    pageSize = pagination.size,
  } = params;

  // Build query parameters for the API
  const queryParams: Record<string, string | number> = {
    page,
    pageSize,
  };

  // Add organizationId if provided (for organization-specific filtering)
  if (organizationId) {
    queryParams.organizationId = organizationId;
  }

  // Add searchText parameter if provided
  if (searchText.trim()) {
    queryParams.searchText = searchText.trim();
  }

  const response = await api.get(`${apiUrl}/list-roles`, {
    params: queryParams,
  });

  // Return the response data directly - the slice will handle the transformation
  return response.data;
};

const fetchRoleById = async (id: string) => {
  const response = await api.get(`${apiUrl}/role`, {
    params: { id },
  });

  return response.data.role;
};

const createRole = async (data: CreateRoleData) => {
  const response = await api.post(`${apiUrl}/role`, data);
  return response.data;
};

const updateRole = async (data: UpdateRoleData) => {
  const response = await api.patch(`${apiUrl}/role`, data);
  return response.data;
};

const deleteRole = async (roleId: string) => {
  const response = await api.delete(`${apiUrl}/role`, {
    params: { roleId },
  });
  return response.data;
};

const roleService = {
  fetchRoles,
  fetchRoleById,
  createRole,
  updateRole,
  deleteRole,
};

export default roleService;
