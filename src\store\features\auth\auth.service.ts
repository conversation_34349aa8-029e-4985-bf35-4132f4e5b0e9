import {
  AUTH_ENDPOINT,
  EMR_USER_INFO_ENDPOINT,
  USERS_ENDPOINT,
} from '../../../constants/api-endpoints';
import api from '../../../services/api';
import { getActiveAccount, getToken } from '../../../utils/authUtils';

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface LoginResponse {
  user: {
    id: string;
    email: string;
    role: string;
    name: string;
    phone?: string | number;
    roles?: Array<{
      id: string;
      name: string;
      description: string;
      permissions: any[];
      organizationId: string;
      isSystem: boolean;
      createdAt: string;
    }>;
    organizationId?: string;
    organization?: any;
    createdAt?: string;
    mustResetPassword?: boolean;
    lastLogin?: string;
  };
  token: string;
}

export interface SetPasswordData {
  resetToken: string;
  password: string;
  confirmPassword: string;
}

export interface SetPasswordResponse {
  message: string;
}

const login = async (credentials: LoginCredentials) => {
  const response = await api.post<LoginResponse>(
    `${AUTH_ENDPOINT}/login`,
    credentials
  );

  if (!response.data || !response.data.user || !response.data.token) {
    throw new Error('Invalid response format from server');
  }

  const { user, token } = response.data;

  // Ensure user has at least one role
  if (!user.roles || !Array.isArray(user.roles) || user.roles.length === 0) {
    user.roles = [
      {
        id: 'role-1',
        name:
          user.role === 'Super Admin'
            ? 'SUPER_ADMIN'
            : user.role
              ? user.role.toUpperCase()
              : 'USER',
        description: user.role || 'User',
        permissions: [],
        organizationId: user.organizationId || '',
        isSystem: true,
        createdAt: new Date().toISOString(),
      },
    ];
  }

  return {
    data: {
      user: {
        ...user,
        roles: user.roles.map((role) => ({
          ...role,
          createdAt: role.createdAt || new Date().toISOString(),
        })),
      },
      token,
    },
  };
};

const setPassword = async (data: SetPasswordData) => {
  const response = await api.post<SetPasswordResponse>(
    `${USERS_ENDPOINT}/user/set-password`,
    data
  );

  return response.data;
};

export interface EmrUserInfo {
  id: string;
  email: string;
  userRole: string;
  name: string;
  roleId: string;
  organizationId: string;
  organizationName: string;
  userType: string;
  isActive: boolean;
  redirectUrl: string;
  permissionKeys: string[];
  phoneNumber?: string;
  [key: string]: any;
}

/**
 * Fetches user information from EMR service by email
 * @param email User's email address
 * @returns Promise with EMR user information including token
 */
const fetchEmrUserInfo = async (
  email: string
): Promise<{ user: EmrUserInfo; token: string }> => {
  const account = getActiveAccount();
  if (!account) {
    console.error('2.1 No active account found');
    throw new Error('No active account found');
  }

  const token = await getToken();
  if (!token) {
    console.error('3.1 No access token available');
    throw new Error('No access token available');
  }

  // Check if we have cached user info in localStorage
  const cachedUserInfo = localStorage.getItem('emrUserInfo');
  if (cachedUserInfo) {
    try {
      const parsedInfo = JSON.parse(cachedUserInfo);
      if (parsedInfo.email === email) {
        return { user: parsedInfo, token };
      }
    } catch (e) {
      console.warn('Failed to parse cached EMR user info', e);
    }
  }

  try {
    // Make the API call
    const response = await api.get<EmrUserInfo>(EMR_USER_INFO_ENDPOINT, {
      params: { email },
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.data) {
      console.error('5.1 No data in response');
      throw new Error('No user data in response from EMR service');
    }

    // Store user info in localStorage for future use
    if (response.data) {
      try {
        localStorage.setItem('emrUserInfo', JSON.stringify(response.data));
        // Also store the role separately for quick access
        if (response.data.userRole) {
          localStorage.setItem('userRole', response.data.userRole);
        }
      } catch (e) {
        console.warn('Failed to store EMR user info in localStorage', e);
      }
    }

    // Format the response to match the expected return type
    return {
      user: response.data,
      token,
    };
  } catch (error) {
    console.error('5.2 API Call failed:', {
      error,
      message: error.message,
      response: error.response?.data,
    });
    throw error;
  }

  // This code is now unreachable due to the try-catch block above
  throw new Error('Unexpected error in fetchEmrUserInfo');
};

const authService = {
  login,
  setPassword,
  fetchEmrUserInfo,
};

export default authService;
