import { yupResolver } from '@hookform/resolvers/yup';
import React, { useEffect } from 'react';
import { Controller, SubmitHandler, useForm } from 'react-hook-form';

import AppButton from '../../../components/Common/AppButton';
import TextInput from '../../../components/Common/MUIInput';
import { useMedicines } from '../hooks/useMedicines';
import { medicineUpdateSchema } from '../schemas/medicine.schema';
import { MedicineListItem } from '../types/medicine.types';

interface MedicineFormProps {
  medicine?: MedicineListItem | null;
  onSuccess: () => void;
  organizationId: string;
  isOpen?: boolean;
  onClose?: () => void;
}

interface FormValues {
  medicineId: string;
  isActive: boolean;
  price: number | '';
}

const MedicineForm: React.FC<MedicineFormProps> = ({
  medicine,
  onSuccess,
  organizationId,
  onClose = () => {},
}) => {
  const { updateMedicinesList } = useMedicines(organizationId);
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<FormValues>({
    resolver: yupResolver(medicineUpdateSchema as any), // Type assertion to handle yup schema type
    defaultValues: {
      medicineId: medicine?.medicineId || '',
      isActive: medicine?.isActive ?? true,
      price:
        medicine?.price ||
        medicine?.organizationPrice ||
        medicine?.defaultCost ||
        '',
    },
  });

  useEffect(() => {
    reset({
      medicineId: medicine?.medicineId || '',
      isActive: medicine?.isActive ?? true,
      price:
        medicine?.price ??
        medicine?.organizationPrice ??
        medicine?.defaultCost ??
        '',
    });
  }, [medicine, reset]);

  const onSubmit: SubmitHandler<FormValues> = async (formData) => {
    if (!medicine) return;

    try {
      const result = await updateMedicinesList({
        organizationId,
        medicines: [
          {
            medicineId: formData.medicineId,
            isActive: formData.isActive,
            price: formData.price === '' ? 0 : formData.price,
          },
        ],
      });

      if (result?.type?.includes('fulfilled')) {
        onSuccess();
        onClose();
      }
    } catch (error) {
      console.error('Error updating medicine:', error);
    }
  };

  if (!medicine) return null;

  return (
    <div className='p-6'>
      <form
        onSubmit={handleSubmit(onSubmit as SubmitHandler<FormValues>)}
        className='space-y-6'
      >
        {/* Hidden medicineId */}
        <Controller
          name='medicineId'
          control={control}
          render={({ field }) => <input type='hidden' {...field} />}
        />
        {/* Product Name - Disabled Input */}
        <div>
          <TextInput
            label='Product Name'
            value={medicine.productName || medicine.name || ''}
            disabled
            InputProps={{ readOnly: true }}
            className='bg-gray-50 text-gray-500'
          />
        </div>
        {/* Organization Cost */}
        <div>
          <Controller
            name='price'
            control={control}
            render={({ field }) => (
              <TextInput
                {...field}
                label='Organization Cost *'
                type='number'
                value={field.value === 0 ? '' : field.value}
                onChange={(e) => {
                  const value = e.target.value;
                  field.onChange(value === '' ? '' : Number(value));
                }}
                error={!!errors.price}
                helperText={errors.price?.message}
                inputProps={{
                  min: 0,
                  step: 0.01,
                }}
                placeholder='Enter amount'
              />
            )}
          />
        </div>
        {/* Status - Temporarily hidden */}
        {/* <div>
          <Controller
            name='isActive'
            control={control}
            render={({ field }) => (
              <SelectInput
                {...field}
                label='Status'
                error={!!errors.isActive}
                helperText={errors.isActive?.message}
                options={[
                  { label: 'Active', value: 'true' },
                  { label: 'Inactive', value: 'false' },
                ]}
              />
            )}
          />
        </div> */}
        {/* Form Actions */}
        <div className='flex justify-end space-x-4 pt-6 border-t border-gray-200'>
          <AppButton
            type='button'
            kind='secondary'
            onClick={onClose}
            disabled={isSubmitting}
          >
            Cancel
          </AppButton>
          <AppButton type='submit' kind='primary' disabled={isSubmitting}>
            {isSubmitting ? 'Saving...' : 'Save Changes'}
          </AppButton>
        </div>
      </form>
    </div>
  );
};

export default MedicineForm;
