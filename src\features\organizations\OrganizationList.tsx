import { Building2, Plus } from 'lucide-react';
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import AppButton from '../../components/Common/AppButton';
import ConfirmationModal from '../../components/Common/ConfirmationModal';
import DataTable from '../../components/Common/DataTable';
import Modal from '../../components/Common/Modal';
import SearchBar from '../../components/Common/SearchBar';
import StatusBadge from '../../components/Common/StatusBadge';
import { useAuth } from '../../hooks/useAuth';
import { useConfirmation } from '../../hooks/useConfirmation';
import { AppDispatch, RootState } from '../../store';
import {
  deleteOrganization,
  fetchOrganizations,
  setPage,
  setSearchName,
} from '../../store/features/organizations/organization.slice';
import { Organization } from '../../types';
import OrganizationForm from './OrganizationForm';

interface TransformedOrganization {
  id: string;
  name: string;
  contactPersonName: string;
  contactEmail: string;
  contactPhone: string;
  status: string;
}

// Removed unused CellInfo interface

// Removed unused Column interface

const OrganizationList: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { selectedOrganization } = useAuth();
  const {
    organizations,
    loading,
    total,
    page,
    limit,
    searchName,
    statusFilter,
  } = useSelector((state: RootState) => state.organizations);

  const [showModal, setShowModal] = React.useState(false);
  const [selectedOrg, setSelectedOrg] = React.useState<Organization | null>(
    null
  );

  // Confirmation modal hook
  const confirmation = useConfirmation();

  // Fetch organizations on component mount and when pagination/search changes
  useEffect(() => {
    // Determine the correct filter parameters based on selected organization
    let nameFilter: string | undefined;

    if (selectedOrganization) {
      // If a specific organization is selected, filter by its name
      nameFilter = selectedOrganization.name;
    } else {
      // If "All Organizations" is selected, use search term only if it exists
      nameFilter = searchName || undefined;
    }

    dispatch(
      fetchOrganizations({
        ...(nameFilter && { name: nameFilter }), // Only include name if it exists
        status: statusFilter,
        page,
        pageSize: limit,
      })
    );
  }, [dispatch, searchName, statusFilter, page, limit, selectedOrganization]);

  // Transform organizations data for the table
  const transformedData: TransformedOrganization[] =
    organizations?.map((org) => ({
      id: org.id,
      name: org.name,
      contactPersonName: org.contactPersonName,
      contactEmail: org.contactEmail,
      contactPhone: org.contactPhone || '-',
      status: org.isActive ? 'active' : 'inactive',
    })) || [];

  const handlePageChange = (newPage: number) => {
    dispatch(setPage(newPage));
  };

  const handleSearch = (value: string) => {
    dispatch(setSearchName(value));
  };

  const handleCreate = () => {
    setSelectedOrg(null);
    setShowModal(true);
  };

  const handleEdit = (org: TransformedOrganization) => {
    const originalOrg = organizations.find((o) => o.id === org.id);
    if (originalOrg) {
      setSelectedOrg(originalOrg);
      setShowModal(true);
    }
  };

  const handleDelete = async (org: TransformedOrganization) => {
    confirmation.confirmDelete(
      org.name,
      async () => {
        await dispatch(deleteOrganization(org.id));
        // Refresh the current page with correct filter logic
        let nameFilter: string | undefined;

        if (selectedOrganization) {
          nameFilter = selectedOrganization.name;
        } else {
          nameFilter = searchName || undefined;
        }

        dispatch(
          fetchOrganizations({
            ...(nameFilter && { name: nameFilter }),
            status: statusFilter,
            page,
            pageSize: limit,
          })
        );
      },
      `Are you sure you want to delete "${org.name}"? This action cannot be undone and will permanently remove all associated data.`
    );
  };

  const handleFormSuccess = () => {
    setShowModal(false);
    // Refresh the current page with correct filter logic
    let nameFilter: string | undefined;

    if (selectedOrganization) {
      nameFilter = selectedOrganization.name;
    } else {
      nameFilter = searchName || undefined;
    }

    dispatch(
      fetchOrganizations({
        ...(nameFilter && { name: nameFilter }),
        status: statusFilter,
        page,
        pageSize: limit,
      })
    );
  };

  // Memoized columns definition (like user list)
  const columns = [
    {
      key: 'name' as const,
      label: 'Name',
      render: (_: unknown, org: TransformedOrganization) => (
        <div className='flex items-center space-x-3'>
          <div className='flex-shrink-0'>
            <div className='w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center'>
              <Building2 className='w-5 h-5 text-blue-600' />
            </div>
          </div>
          <div>
            <div className='font-medium text-gray-900'>{org.name}</div>
            <div className='text-sm text-gray-500'>{org.contactEmail}</div>
          </div>
        </div>
      ),
      sortable: true,
    },
    {
      key: 'contactPersonName' as const,
      label: 'Contact Person',
      render: (_: unknown, org: TransformedOrganization) =>
        org.contactPersonName,
      sortable: true,
    },
    {
      key: 'contactEmail' as const,
      label: 'Contact Email',
      render: (_: unknown, org: TransformedOrganization) => org.contactEmail,
    },
    {
      key: 'contactPhone' as const,
      label: 'Contact Phone',
      render: (_: unknown, org: TransformedOrganization) => org.contactPhone,
    },
    {
      key: 'status' as const,
      label: 'Status',
      render: (_: unknown, org: TransformedOrganization) => (
        <StatusBadge status={org.status as 'active' | 'inactive'} size='sm' />
      ),
    },
  ];

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4'>
        <div>
          <h1 className='text-2xl font-bold text-gray-900'>Organizations</h1>
          <p className='mt-1 text-sm text-gray-500'>
            Manage healthcare organizations in the system
          </p>
        </div>
        <div className='flex items-center gap-3'>
          <SearchBar
            value={searchName}
            onChange={handleSearch}
            placeholder='Search organizations...'
            size='md'
          />
          <AppButton
            onClick={handleCreate}
            startIcon={<Plus className='w-4 h-4' />}
          >
            Add Organization
          </AppButton>
        </div>
      </div>

      {/* Data Table */}
      <DataTable
        data={transformedData}
        columns={columns}
        loading={loading}
        pagination={{
          page,
          limit,
          total,
          onPageChange: handlePageChange,
        }}
        onEdit={handleEdit}
        onDelete={handleDelete}
      />

      {/* Modal */}
      <Modal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        title={selectedOrg ? 'Edit Organization' : 'Create Organization'}
        size='md'
      >
        <OrganizationForm
          organization={selectedOrg}
          onSuccess={handleFormSuccess}
          onCancel={() => setShowModal(false)}
        />
      </Modal>

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={confirmation.isOpen}
        onClose={confirmation.hideConfirmation}
        onConfirm={confirmation.handleConfirm}
        title={confirmation.title}
        message={confirmation.message}
        confirmText={confirmation.confirmText || 'Confirm'}
        cancelText={confirmation.cancelText || 'Cancel'}
        type={confirmation.type || 'danger'}
        loading={confirmation.loading}
        {...(confirmation.itemName && { itemName: confirmation.itemName })}
      />
    </div>
  );
};

export default OrganizationList;
