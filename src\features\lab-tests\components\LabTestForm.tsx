import { yupResolver } from '@hookform/resolvers/yup';
import React, { useCallback, useEffect } from 'react';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';

import AppButton from '../../../components/Common/AppButton';
import TextInput from '../../../components/Common/MUIInput';
import { LabTestListItem } from '../types/labTest.types';

// Form schema
const labTestFormSchema = yup.object({
  organizationCost: yup
    .number()
    .typeError('Please enter a valid number')
    .positive('Cost must be greater than 0')
    .optional()
    .nullable()
    .transform((value) => (isNaN(value) || value === '' ? undefined : value)),
  department: yup.string().optional().default(''),
  isActive: yup.boolean().required('Please select status'),
});

interface LabTestFormData {
  organizationCost: number | '';
  department: string;
  isActive: boolean;
}

interface LabTestFormProps {
  test: LabTestListItem | null;
  onSubmit: (
    testId: string,
    data: {
      organizationCost: number;
      organizationPrice?: number; // For backward compatibility
      department?: string;
      departments?: string[];
      isActive: boolean;
    }
  ) => Promise<void>;
  onSuccess?: () => void;
  onClose: () => void;
  updating?: boolean;
}

const LabTestForm: React.FC<LabTestFormProps> = ({
  test,
  onSubmit,
  onSuccess,
  onClose,
  updating = false,
}) => {
  // Removed unused department state variables

  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<LabTestFormData>({
    resolver: yupResolver(labTestFormSchema) as any, // Type assertion to handle yup schema type
    defaultValues: {
      organizationCost: test?.organizationCost ?? test?.organizationPrice ?? '',
      department: '',
      isActive: true,
    },
  });

  // Removed unused watch variables

  // Removed unused department loading useEffect

  // Reset form when test changes
  useEffect(() => {
    if (test) {
      const department = (
        test.departments && test.departments.length > 0
          ? test.departments[0]
          : ''
      ) as string;
      reset({
        organizationCost: test.organizationCost ?? test.organizationPrice ?? '',
        department,
        isActive: test.isActive,
      });
    } else {
      reset({
        organizationCost: '',
        department: '',
        isActive: true,
      });
    }
  }, [test, reset]);

  const onFormSubmit = useCallback(
    async (data: LabTestFormData) => {
      if (!test) return;

      try {
        // For backward compatibility, we'll send both organizationCost and organizationPrice
        // Also include both department (string) and departments (array)
        const department = data.department || '';
        const departments = department ? [department] : [];
        const cost =
          data.organizationCost === '' ? 0 : (data.organizationCost ?? 0); // Default to 0 if empty or null

        await onSubmit(test.testId, {
          organizationCost: cost,
          organizationPrice: cost, // For backward compatibility
          department, // For backward compatibility
          departments, // New format with array
          isActive: data.isActive,
        });

        onSuccess?.();
        onClose();
      } catch (error) {
        console.error('Error saving test:', error);
        // Error is already handled by the parent component
      }
    },
    [test, onSubmit, onSuccess, onClose]
  );

  if (!test) return null;

  return (
    <form onSubmit={handleSubmit(onFormSubmit)} className='space-y-6 p-6'>
      {/* Test Name - Disabled Input */}
      <TextInput
        value={test.testName}
        label='Test Name'
        disabled
        placeholder='Test name'
      />

      {/* Organization Cost */}
      <Controller
        name='organizationCost'
        control={control}
        render={({ field }) => (
          <TextInput
            {...field}
            label='Organization Cost *'
            value={field.value === 0 ? '' : field.value}
            onChange={(e) => {
              const value = e.target.value;
              field.onChange(value === '' ? '' : Number(value));
            }}
            error={!!errors.organizationCost}
            helperText={errors.organizationCost?.message}
            placeholder='Enter amount'
            type='number'
            inputProps={{
              min: 0,
              step: 0.01,
            }}
          />
        )}
      />

      {/* Department Select (hidden for now) */}
      {/* <Controller
        name='department'
        control={control}
        render={({ field }) => (
          <SelectInput
            {...field}
            label='Department'
            error={!!errors.department}
            helperText={errors.department?.message}
            options={[
              { label: 'Select Department', value: '' },
              ...departments.map((d) => ({
                label: d.label,
                value: d.value,
              })),
            ]}
            disabled={loadingDepartments}
          />
        )}
      /> */}

      {/* Status - Temporarily hidden */}
      {/* <Controller
        name='isActive'
        control={control}
        render={({ field }) => (
          <SelectInput
            {...field}
            label='Status *'
            error={!!errors.isActive}
            helperText={errors.isActive?.message}
            options={[
              { label: 'Active', value: 'true' },
              { label: 'Inactive', value: 'false' },
            ]}
            onChange={(e) => field.onChange(e.target.value === 'true')}
            value={field.value ? 'true' : 'false'}
          />
        )}
      /> */}

      {/* Form Actions */}
      <div className='flex justify-end space-x-4 pt-4'>
        <AppButton
          type='button'
          kind='secondary'
          onClick={onClose}
          disabled={isSubmitting || updating}
        >
          Cancel
        </AppButton>
        <AppButton
          type='submit'
          kind='primary'
          disabled={isSubmitting || updating}
        >
          {isSubmitting || updating ? 'Saving...' : 'Save'}
        </AppButton>
      </div>
    </form>
  );
};

export default LabTestForm;
