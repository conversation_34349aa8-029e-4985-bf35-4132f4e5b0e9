import * as yup from 'yup';

export const userFormSchema = yup.object().shape({
  name: yup
    .string()
    .required('Name is required')
    .min(2, 'Name must be at least 2 characters')
    .max(100, 'Name must not exceed 100 characters')
    .matches(/^[a-zA-Z\s]+$/, 'Name can only contain letters and spaces'),
  email: yup
    .string()
    .required('Email is required')
    .email('Please enter a valid email address')
    .max(100, 'Email must not exceed 100 characters'),
  userRole: yup
    .string()
    .required('User role is required')
    .min(1, 'Please select a role'),
  roleId: yup.string().required('Role ID is required'),
  status: yup
    .string()
    .oneOf(['active', 'inactive'], 'Invalid status')
    .required('Status is required'),
});

export type UserFormSchema = yup.InferType<typeof userFormSchema>;
