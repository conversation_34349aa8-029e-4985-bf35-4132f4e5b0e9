import { format } from 'date-fns';
import { User as UserIcon } from 'lucide-react';
import React, { memo, useMemo } from 'react';

import DataTable, { Column } from '../../../components/Common/DataTable';
import SelectInput from '../../../components/Common/MUISelect';
import SearchBar from '../../../components/Common/SearchBar';
import Tooltip from '../../../components/Common/Tooltip';
import DatePicker from '../../../components/DatePicker';
import { genderFilterOptions } from '../../../constants/status-options';
import { usePatients } from '../hooks/usePatients';
import { Patient } from '../types/patient.types';

interface PatientListProps {
  organizationName: string;
  organizationId?: string;
}

const PatientList: React.FC<PatientListProps> = memo(
  ({ organizationName, organizationId }) => {
    const {
      patients,
      loading,
      total,
      page,
      limit,
      searchText,
      genderFilter,
      fromAge,
      toAge,
      fromDate,
      toDate,
      search,
      filterByGender,
      filterByAgeRange,
      filterByDateRange,
      changePage,
      clearFilters,
    } = usePatients(organizationId);

    // Memoized columns definition
    const columns: Column<Patient>[] = useMemo(
      () => [
        {
          key: 'id',
          label: 'Patient ID',
          width: 'w-32',
        },
        {
          key: 'fullName',
          label: 'Full Name',
          render: (_, patient) => (
            <div className='flex items-center space-x-3'>
              <div className='flex-shrink-0'>
                <div className='w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center'>
                  <UserIcon className='w-5 h-5 text-blue-600' />
                </div>
              </div>
              <div>
                <Tooltip
                  content={
                    patient.fullName ||
                    `${patient.firstName} ${patient.lastName}`
                  }
                >
                  <span className='w-[120px] overflow-hidden whitespace-nowrap truncate block cursor-help font-medium text-gray-900'>
                    {patient.fullName ||
                      `${patient.firstName} ${patient.lastName}`}
                  </span>
                </Tooltip>
              </div>
            </div>
          ),
          width: 'w-[120px]',
        },
        {
          key: 'age',
          label: 'Age',
          width: 'w-20',
        },
        {
          key: 'gender',
          label: 'Gender',
          width: 'w-24',
          render: (gender) => (
            <span className='inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800'>
              {gender}
            </span>
          ),
        },
        {
          key: 'contactPhone',
          label: 'Contact Phone',
          width: 'w-32',
          render: (phone) => phone || '-',
        },
        {
          key: 'email',
          label: 'Email',
          width: 'w-48',
          render: (email) => email || '-',
        },
        {
          key: 'registrationDate',
          label: 'Registration Date',
          width: 'w-40',
          render: (date) => format(new Date(date), 'dd/MM/yy'),
        },
      ],
      []
    );

    // Handle search change
    const handleSearchChange = (value: string) => {
      search(value);
    };

    // Handle gender filter change
    const handleGenderFilterChange = (value: string) => {
      filterByGender(value);
    };

    // Handle page change
    const handlePageChange = (newPage: number) => {
      changePage(newPage);
    };

    return (
      <div className='space-y-6'>
        <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4'>
          <div>
            <h1 className='text-2xl font-bold text-gray-900'>Patients</h1>
            <p className='mt-1 text-sm text-gray-500'>
              Administrative view of patients for {organizationName}
            </p>
          </div>
          <div className='flex-shrink-0'>
            <SearchBar
              value={searchText}
              onChange={handleSearchChange}
              placeholder='Search patients...'
              size='md'
            />
          </div>
        </div>

        <DataTable<Patient>
          columns={columns}
          data={patients}
          loading={loading}
          searchFilters={
            <div className='flex gap-3 items-center'>
              <SelectInput
                value={genderFilter}
                onChange={(e) => handleGenderFilterChange(e.target.value)}
                options={genderFilterOptions}
                size='small'
                sx={{ minWidth: 180, height: 20, mb: 3 }}
              />

              <div className='flex items-center space-x-2'>
                <span className='text-sm text-gray-600 whitespace-nowrap'>
                  Age:
                </span>
                <input
                  type='number'
                  placeholder='From'
                  min='0'
                  value={fromAge ?? ''}
                  onChange={(e) => {
                    const value = e.target.value;
                    const numValue =
                      value === '' ? null : Math.max(0, parseInt(value) || 0);
                    filterByAgeRange(numValue, toAge);
                  }}
                  onKeyDown={(e) => {
                    // Prevent minus key, 'e', and '.'
                    if (['-', 'e', 'E', '.'].includes(e.key)) {
                      e.preventDefault();
                    }
                  }}
                  onWheel={(e) => e.currentTarget.blur()} // Prevent number change on scroll
                  className='w-20 px-2 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm'
                />
                <span className='text-gray-400'>-</span>
                <input
                  type='number'
                  placeholder='To'
                  min='0'
                  value={toAge ?? ''}
                  onChange={(e) => {
                    const value = e.target.value;
                    const numValue =
                      value === '' ? null : Math.max(0, parseInt(value) || 0);
                    filterByAgeRange(fromAge, numValue);
                  }}
                  onKeyDown={(e) => {
                    // Prevent minus key, 'e', and '.'
                    if (['-', 'e', 'E', '.'].includes(e.key)) {
                      e.preventDefault();
                    }
                  }}
                  onWheel={(e) => e.currentTarget.blur()} // Prevent number change on scroll
                  className='w-20 px-2 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm'
                />
              </div>

              <div className='flex items-center space-x-2'>
                <span className='text-sm text-gray-600 whitespace-nowrap'>
                  Registration:
                </span>
                <div className='flex items-center space-x-1'>
                  <DatePicker
                    value={fromDate ? new Date(fromDate) : null}
                    onChange={(date) => {
                      const formatted = date
                        ? `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
                        : '';
                      // Always pass the current toDate, it will be handled in the hook
                      filterByDateRange(formatted, toDate);
                    }}
                    placeholder='From'
                    className='px-2 py-1 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm min-w-[120px]'
                  />
                </div>
                <span className='text-gray-400'>to</span>
                <div className='flex items-center space-x-1'>
                  <DatePicker
                    value={toDate ? new Date(toDate) : null}
                    onChange={(date) => {
                      const formatted = date
                        ? `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
                        : '';
                      // If only toDate is being set, the hook will handle not making the API call
                      filterByDateRange(fromDate, formatted);
                    }}
                    placeholder='To'
                    className='px-2 py-1 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm min-w-[120px]'
                  />
                </div>
              </div>

              <button
                onClick={clearFilters}
                className='px-3 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50 whitespace-nowrap'
              >
                Clear Filters
              </button>
            </div>
          }
          pagination={{
            total,
            page,
            limit,
            onPageChange: handlePageChange,
          }}
        />
      </div>
    );
  }
);

PatientList.displayName = 'PatientList';

export default PatientList;
