import {
  createAsyncThunk,
  createSlice,
  isPending,
  isRejected,
} from '@reduxjs/toolkit';

import { Patient } from '../../../types';
import { calculateAge } from '../../../utils/date-utils';
import { ApiError, InitialState } from '../../../utils/reducer-utils';
import patientService, { PatientParams } from './patient.service';

type ExtendedInitialState = InitialState<Patient> & {
  patients: Patient[];
  currentPatient: Patient | null;
  total: number;
  totalPages: number;
  page: number;
  limit: number;
  searchText: string;
  genderFilter: string;
  fromAge: number | null;
  toAge: number | null;
  fromDate: string;
  toDate: string;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  error: string | null;
};

const initialState: ExtendedInitialState = {
  loading: false,
  updating: false,
  updateSuccess: false,
  entity: null,
  entities: [],
  errorMessage: null,
  successMessage: null,
  allEntities: [],
  patients: [],
  currentPatient: null,
  total: 0,
  totalPages: 0,
  page: 1,
  limit: 100, // As per user preference
  searchText: '',
  genderFilter: '', // Matches genderFilterOptions "All Genders" value
  fromAge: null,
  toAge: null,
  fromDate: '',
  toDate: '',
  sortBy: 'id',
  sortOrder: 'desc',
  error: null,
};

export const fetchPatients = createAsyncThunk(
  'patients/fetchPatients',
  async (params: PatientParams, { rejectWithValue }) => {
    try {
      return await patientService.fetchPatients(params);
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

const patientSlice = createSlice({
  name: 'patients',
  initialState,
  reducers: {
    setSearchText: (state, action) => {
      state.searchText = action.payload;
      state.page = 1; // Reset to first page when searching
    },
    setGenderFilter: (state, action) => {
      state.genderFilter = action.payload;
      state.page = 1;
    },
    setAgeRange: (state, action) => {
      state.fromAge = action.payload.fromAge;
      state.toAge = action.payload.toAge;
      state.page = 1;
    },
    setDateRange: (state, action) => {
      state.fromDate = action.payload.fromDate;
      state.toDate = action.payload.toDate;
      state.page = 1;
    },
    setSorting: (state, action) => {
      state.sortBy = action.payload.sortBy;
      state.sortOrder = action.payload.sortOrder;
    },
    setPage: (state, action) => {
      state.page = action.payload;
    },
    clearFilters: (state) => {
      state.searchText = '';
      state.genderFilter = '';
      state.fromAge = null;
      state.toAge = null;
      state.fromDate = '';
      state.toDate = '';
      state.page = 1;
    },
    clearMessages: (state) => {
      state.errorMessage = null;
      state.successMessage = null;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchPatients.fulfilled, (state, action) => {
        state.loading = false;
        state.patients = action.payload.items.map((item: any) => {
          // Calculate age from DOB if available, otherwise use 0
          const age = item.dob ? calculateAge(item.dob) : 0;

          return {
            id: item.id,
            firstName: item.name?.split(' ')[0] || '',
            lastName: item.name?.split(' ').slice(1).join(' ') || '',
            fullName: item.name,
            age,
            dob: item.dob || '',
            gender: item.sex, // API uses 'sex' field
            contactPhone: item.contact?.phone || '',
            email: item.contact?.email || '',
            organizationId: item.organizationId,
            registrationDate: new Date(item.created_on),
            createdAt: new Date(item.created_on),
            updatedAt: new Date(item.updated_on),
          };
        });
        state.total = action.payload.totalItemCount;
        state.totalPages = action.payload.totalPages;
        state.error = null;
        state.errorMessage = null;
      })
      .addMatcher(isPending(fetchPatients), (state) => {
        state.loading = true;
        state.error = null;
        state.errorMessage = null;
      })
      .addMatcher(isRejected(fetchPatients), (state, action) => {
        state.loading = false;
        const error = action.payload as ApiError;
        state.error = error?.message || 'Failed to fetch patients';
        state.errorMessage = error?.message || 'Failed to fetch patients';
      });
  },
});

export const {
  setSearchText,
  setGenderFilter,
  setAgeRange,
  setDateRange,
  setSorting,
  setPage,
  clearFilters,
  clearMessages,
} = patientSlice.actions;

export default patientSlice.reducer;
