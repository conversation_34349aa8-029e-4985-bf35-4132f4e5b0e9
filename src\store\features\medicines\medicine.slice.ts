import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';

import { removeMedicines as removeMedicinesService } from '../../../features/medicines/services/medicine.service';
import {
  MedicineListItem,
  MedicineListParams,
  UpdateMedicineRequest,
} from '../../../features/medicines/types/medicine.types';
import medicineService from './medicine.service';

interface MedicineState {
  medicines: MedicineListItem[];
  loading: boolean;
  updating: boolean;
  error: string | null;
  successMessage: string | null;
  total: number;
  page: number;
  limit: number;
  searchText: string;
  isActiveFilter: string;
  selectedMedicines: string[];
  isAllSelected: boolean;
  continuationToken: string;
  hasMoreResults: boolean;
  totalFetched: number;
  totalPages: number;
}

const initialState: MedicineState = {
  medicines: [],
  loading: false,
  updating: false,
  error: null,
  successMessage: null,
  total: 0,
  page: 1,
  limit: 100,
  searchText: '',
  isActiveFilter: 'all',
  selectedMedicines: [],
  isAllSelected: false,
  continuationToken: '',
  hasMoreResults: false,
  totalFetched: 0,
  totalPages: 1,
};

export const fetchMedicines = createAsyncThunk(
  'medicines/fetchMedicines',
  async (params: MedicineListParams) => {
    const response = await medicineService.fetchMedicinesList(params);
    return response;
  }
);

export const updateMedicines = createAsyncThunk(
  'medicines/updateMedicines',
  async (data: UpdateMedicineRequest) => {
    const response = await medicineService.updateMedicines(data);
    return response;
  }
);

export const removeMedicines = createAsyncThunk(
  'medicines/removeMedicines',
  async (data: {
    organizationId: string;
    medicines?: string[];
    selectAll?: boolean;
  }) => {
    const response = await removeMedicinesService(data);
    return response;
  }
);

const medicineSlice = createSlice({
  name: 'medicines',
  initialState,
  reducers: {
    setSearchText: (state, action: PayloadAction<string>) => {
      state.searchText = action.payload;
      state.page = 1;
    },
    setIsActiveFilter: (state, action: PayloadAction<string>) => {
      state.isActiveFilter = action.payload;
      state.page = 1;
    },
    setPage: (state, action: PayloadAction<number>) => {
      state.page = action.payload;
    },
    setLimit: (state, action: PayloadAction<number>) => {
      state.limit = action.payload;
      state.page = 1;
    },
    selectMedicine: (state, action: PayloadAction<string>) => {
      const medicineId = action.payload;
      if (state.selectedMedicines.includes(medicineId)) {
        state.selectedMedicines = state.selectedMedicines.filter(
          (id) => id !== medicineId
        );
      } else {
        state.selectedMedicines.push(medicineId);
      }
    },
    selectAllMedicines: (state) => {
      if (state.isAllSelected) {
        state.selectedMedicines = [];
        state.isAllSelected = false;
      } else {
        state.selectedMedicines = state.medicines.map(
          (medicine) => medicine.medicineId
        );
        state.isAllSelected = true;
      }
    },
    clearSelection: (state) => {
      state.selectedMedicines = [];
      state.isAllSelected = false;
    },
    updateMedicineInList: (
      state,
      action: PayloadAction<{
        medicineId: string;
        updates: Partial<MedicineListItem>;
      }>
    ) => {
      const { medicineId, updates } = action.payload;
      const index = state.medicines.findIndex(
        (medicine) => medicine.medicineId === medicineId
      );
      if (index !== -1) {
        const medicine = state.medicines[index];
        if (medicine) {
          Object.assign(medicine, updates);
        }
      }
    },
    clearError: (state) => {
      state.error = null;
    },
    clearSuccessMessage: (state) => {
      state.successMessage = null;
    },
    clearMessages: (state) => {
      state.error = null;
      state.successMessage = null;
    },
    resetMedicineState: () => initialState,
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchMedicines.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchMedicines.fulfilled, (state, action) => {
        state.loading = false;
        state.medicines = action.payload.medicines;
        state.total = action.payload.totalRecords;
        state.page = action.payload.currentPage;
        state.continuationToken = action.payload.continuationToken || '';
        state.hasMoreResults = action.payload.hasMoreResults || false;
        state.totalFetched = action.payload.totalFetched || 0;
        state.totalPages = action.payload.totalPages || 1;
        if (state.isAllSelected) {
          state.selectedMedicines = action.payload.medicines.map(
            (medicine: MedicineListItem) => medicine.medicineId
          );
        } else {
          state.selectedMedicines = [];
        }
      })
      .addCase(fetchMedicines.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch medicines';
      })
      .addCase(updateMedicines.pending, (state) => {
        state.updating = true;
        state.error = null;
      })
      .addCase(updateMedicines.fulfilled, (state, action) => {
        state.updating = false;
        state.successMessage =
          action.payload.message || 'Medicines updated successfully';
        state.selectedMedicines = [];
      })
      .addCase(updateMedicines.rejected, (state, action) => {
        state.updating = false;
        state.error = action.error.message || 'Failed to update medicines';
      })
      .addCase(removeMedicines.pending, (state) => {
        state.updating = true;
        state.error = null;
      })
      .addCase(removeMedicines.fulfilled, (state, action) => {
        state.updating = false;
        state.successMessage =
          action.payload.message || 'Medicines removed successfully';
        state.selectedMedicines = [];
        state.isAllSelected = false;
      })
      .addCase(removeMedicines.rejected, (state, action) => {
        state.updating = false;
        state.error = action.error.message || 'Failed to remove medicines';
      });
  },
});

export const {
  setSearchText,
  setIsActiveFilter,
  setPage,
  setLimit,
  selectMedicine,
  selectAllMedicines,
  clearSelection,
  updateMedicineInList,
  clearError,
  clearSuccessMessage,
  clearMessages,
  resetMedicineState,
} = medicineSlice.actions;

export default medicineSlice.reducer;
