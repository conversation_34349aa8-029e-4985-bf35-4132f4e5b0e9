import { useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { AppDispatch, RootState } from '../../../store';
import {
  clearError,
  clearMessages,
  clearSelection,
  clearSuccessMessage,
  exportLabTests,
  fetchLabTests,
  removeLabTests,
  selectAllTests,
  selectTest,
  setDepartmentFilter,
  setIsActiveFilter,
  setLimit,
  setPage,
  setSearchText,
  updateLabTests,
  updateTestInList,
} from '../../../store/features/lab-tests/labTest.slice';
import { getDepartmentFilterValue } from '../services/department.service';
import {
  LabTestListItem,
  LabTestListParams,
  UpdateLabTestRequest,
} from '../types/labTest.types';

export const useLabTests = (organizationId?: string) => {
  const dispatch = useDispatch<AppDispatch>();
  const {
    tests,
    loading,
    updating,
    error,
    successMessage,
    total,
    page,
    limit,
    searchText,
    departmentFilter,
    isActiveFilter,
    selectedTests,
    isAllSelected,
    continuationToken,
    hasMoreResults,
    totalFetched,
    totalPages,
    bulkUpdate,
  } = useSelector((state: RootState) => state.labTests);

  // Fetch lab tests with current filters
  const fetchLabTestsList = useCallback(
    (customParams?: Partial<LabTestListParams>) => {
      if (!organizationId) return;
      const params: LabTestListParams = {
        organizationId,
        page,
        pageSize: limit,
        ...(searchText && { searchText }),
        ...(departmentFilter !== 'all' && {
          department: getDepartmentFilterValue(departmentFilter),
        }),
        ...(isActiveFilter !== 'all' && {
          isActive: isActiveFilter === 'active',
        }),
        ...customParams,
      };
      dispatch(fetchLabTests(params));
    },
    [
      dispatch,
      organizationId,
      page,
      limit,
      searchText,
      departmentFilter,
      isActiveFilter,
    ]
  );

  // Update lab tests
  const updateLabTestsList = useCallback(
    async (data: UpdateLabTestRequest) => {
      const result = await dispatch(updateLabTests(data));
      // Only refresh the list for non-bulk updates
      // Bulk updates will be handled by the BulkUpdateStatusIndicator component
      if (updateLabTests.fulfilled.match(result)) {
        const payload = result.payload as { async?: boolean };
        if (!payload?.async) {
          fetchLabTestsList();
        }
      }
      return result;
    },
    [dispatch, fetchLabTestsList]
  );

  // Export lab tests
  const exportLabTestsList = useCallback(() => {
    if (!organizationId) return;

    const params: LabTestListParams = {
      organizationId,
      ...(searchText && { searchText }),
      ...(departmentFilter !== 'all' && {
        department: getDepartmentFilterValue(departmentFilter),
      }),
      ...(isActiveFilter !== 'all' && {
        isActive: isActiveFilter === 'active',
      }),
    };

    dispatch(exportLabTests(params));
  }, [dispatch, organizationId, searchText, departmentFilter, isActiveFilter]);

  // Search functionality
  const search = useCallback(
    (searchTerm: string) => {
      dispatch(setSearchText(searchTerm));
    },
    [dispatch]
  );

  // Filter by department
  const filterByDepartment = useCallback(
    (department: string) => {
      dispatch(setDepartmentFilter(department));
    },
    [dispatch]
  );

  // Filter by status
  const filterByStatus = useCallback(
    (status: string) => {
      dispatch(setIsActiveFilter(status));
    },
    [dispatch]
  );

  // Pagination
  const changePage = useCallback(
    (newPage: number) => {
      dispatch(setPage(newPage));
    },
    [dispatch]
  );

  const changeLimit = useCallback(
    (newLimit: number) => {
      dispatch(setLimit(newLimit));
    },
    [dispatch]
  );

  // Selection management
  const toggleTestSelection = useCallback(
    (testId: string, selected?: boolean) => {
      if (selected === undefined) {
        // Toggle if no selected parameter provided
        dispatch(selectTest(testId));
      } else {
        // Set specific selection state
        dispatch(selectTest({ testId, selected }));
      }
    },
    [dispatch]
  );

  const toggleSelectAll = useCallback(() => {
    dispatch(selectAllTests());
  }, [dispatch]);

  const clearTestSelection = useCallback(() => {
    dispatch(clearSelection());
  }, [dispatch]);

  // Update individual test in the list (for optimistic updates)
  const updateTestInListLocal = useCallback(
    (testId: string, updates: Partial<LabTestListItem>) => {
      dispatch(updateTestInList({ testId, updates }));
    },
    [dispatch]
  );

  // Message management
  const clearErrorMessage = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  const clearSuccessMsg = useCallback(() => {
    dispatch(clearSuccessMessage());
  }, [dispatch]);

  const clearAllMessages = useCallback(() => {
    dispatch(clearMessages());
  }, [dispatch]);

  // Clear selection when component unmounts
  useEffect(() => {
    return () => {
      // Only clear selection when unmounting, not when page changes
      clearTestSelection();
    };
  }, [clearTestSelection]);

  // Auto-fetch when dependencies change
  useEffect(() => {
    if (organizationId) {
      fetchLabTestsList();
    }
  }, [organizationId, fetchLabTestsList]);

  return {
    // Data
    tests,
    loading,
    updating,
    error,
    successMessage,
    total,
    page,
    limit,
    searchText,
    departmentFilter,
    isActiveFilter,
    selectedTests,
    isAllSelected,
    continuationToken,
    hasMoreResults,
    totalFetched,
    totalPages,
    bulkUpdate,

    // Actions
    fetchLabTests: fetchLabTestsList,
    updateLabTests: updateLabTestsList,
    exportLabTests: exportLabTestsList,
    search,
    filterByDepartment,
    filterByStatus,
    changePage,
    changeLimit,
    selectTest: toggleTestSelection,
    selectAllTests: toggleSelectAll,
    clearSelection: clearTestSelection,
    updateTestInList: updateTestInListLocal,
    removeLabTests: async () => {
      if (!organizationId)
        return { success: false, message: 'No organization selected' };

      const request = isAllSelected
        ? {
            organizationId,
            selectAll: true,
            department: getDepartmentFilterValue(departmentFilter),
          }
        : { organizationId, tests: selectedTests };

      const result = await dispatch(removeLabTests(request));

      if (removeLabTests.fulfilled.match(result)) {
        // Refresh the list after successful removal
        fetchLabTestsList();
      }

      return result.payload;
    },
    clearError: clearErrorMessage,
    clearSuccessMessage: clearSuccessMsg,
    clearMessages: clearAllMessages,

    // Computed values
    hasSelection: selectedTests.length > 0 || isAllSelected,
    selectedCount: isAllSelected ? total : selectedTests.length,
  };
};
