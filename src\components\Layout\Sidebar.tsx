import {
  Building,
  ChevronDown,
  ChevronRight,
  Database,
  FileText,
  Shield,
  UserPlus,
  Users,
} from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { Link, useLocation } from 'react-router-dom';

import { PATHS } from '../../constants/paths';
import { useAuth } from '../../hooks/useAuth';

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

interface MenuItem {
  id: string;
  path: string;
  label: string;
  icon: React.ReactNode;
  children?: MenuItem[];
  superAdminOnly?: boolean;
  adminOnly?: boolean;
}

const menuItems: MenuItem[] = [
  // {
  //   id: 'dashboard',
  //   path: PATHS.DASHBOARD,
  //   label: 'Dashboard',
  //   icon: <Activity className='w-5 h-5' />,
  // },
  {
    id: 'organizations',
    path: PATHS.ORGANIZATIONS,
    label: 'Organizations',
    icon: <Building className='w-5 h-5' />,
    superAdminOnly: true,
  },
  {
    id: 'users',
    path: '#',
    label: 'User Management',
    icon: <Users className='w-5 h-5' />,
    children: [
      {
        id: 'users-list',
        path: PATHS.USERS,
        label: 'Users',
        icon: <UserPlus className='w-4 h-4' />,
      },
      {
        id: 'roles',
        path: PATHS.ROLES,
        label: 'Roles & Permissions',
        icon: <Shield className='w-4 h-4' />,
      },
    ],
    adminOnly: true,
  },
  {
    id: 'master-data',
    path: '#',
    label: 'Master Data',
    icon: <Database className='w-5 h-5' />,
    children: [
      // TODO: Implement these master data features in future
      // {
      //   id: 'departments',
      //   path: PATHS.DEPARTMENTS,
      //   label: 'Departments',
      //   icon: <Building2 className='w-4 h-4' />,
      // },
      // {
      //   id: 'branches',
      //   path: PATHS.BRANCHES,
      //   label: 'Branches',
      //   icon: <Building className='w-4 h-4' />,
      // },
      // {
      //   id: 'banks',
      //   path: PATHS.BANKS,
      //   label: 'Banks',
      //   icon: <FileText className='w-4 h-4' />,
      // },
      // {
      //   id: 'languages',
      //   path: PATHS.LANGUAGES,
      //   label: 'Languages',
      //   icon: <FileText className='w-4 h-4' />,
      // },
      // {
      //   id: 'vitals',
      //   path: PATHS.VITALS,
      //   label: 'Vitals',
      //   icon: <Heart className='w-4 h-4' />,
      // },
      // {
      //   id: 'templates',
      //   path: PATHS.TEMPLATES,
      //   label: 'Templates',
      //   icon: <FileText className='w-4 h-4' />,
      // },

      // Laboratory - LOINC integration
      {
        id: 'lab-tests',
        path: '/lab-tests',
        label: 'Laboratory',
        icon: <FileText className='w-4 h-4' />,
      },
      {
        id: 'medicines',
        path: PATHS.MEDICINES,
        label: 'Pharmacy',
        icon: <FileText className='w-4 h-4' />,
      },
    ],
    adminOnly: true,
  },
  {
    id: 'admin-views',
    path: '#',
    label: 'Administrative Views',
    icon: <FileText className='w-5 h-5' />,
    children: [
      {
        id: 'patients',
        path: PATHS.PATIENTS,
        label: 'Patients',
        icon: <Users className='w-4 h-4' />,
      },
      // TODO: Implement Doctors management functionality in future
      // {
      //   id: 'doctors',
      //   path: PATHS.DOCTORS,
      //   label: 'Doctors',
      //   icon: <Stethoscope className='w-4 h-4' />,
      // },
    ],
    adminOnly: true,
  },
  // TODO: Implement System Configuration functionality in future
  // {
  //   id: 'system',
  //   path: PATHS.SYSTEM,
  //   label: 'System Configuration',
  //   icon: <Settings className='w-5 h-5' />,
  //   adminOnly: true,
  // },
  // TODO: Implement Audit Logs functionality in future
  // {
  //   id: 'audit',
  //   path: PATHS.AUDIT,
  //   label: 'Audit Logs',
  //   icon: <Activity className='w-5 h-5' />,
  //   adminOnly: true,
  // },
];

const Sidebar: React.FC<SidebarProps> = ({ isOpen, onClose }) => {
  const { isSuperAdmin, isOrganizationAdmin } = useAuth();
  const location = useLocation();
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const findParentMenuForPath = (path: string): string | null => {
    for (const item of menuItems) {
      if (item.children) {
        for (const child of item.children) {
          if (child.path === path) {
            return item.id;
          }
        }
      }
    }
    return null;
  };

  useEffect(() => {
    const parentId = findParentMenuForPath(location.pathname);
    if (parentId && !expandedItems.includes(parentId)) {
      setExpandedItems((prev) => [...prev, parentId]);
    }
  }, [location.pathname, expandedItems]);

  const toggleExpanded = (itemId: string) => {
    setExpandedItems((prev) =>
      prev.includes(itemId)
        ? prev.filter((id) => id !== itemId)
        : [...prev, itemId]
    );
  };

  const shouldShowItem = (item: MenuItem): boolean => {
    if (item.superAdminOnly && !isSuperAdmin) return false;
    if (item.adminOnly && !isSuperAdmin && !isOrganizationAdmin) return false;
    return true;
  };

  const renderMenuItem = (item: MenuItem, level = 0) => {
    if (!shouldShowItem(item)) return null;

    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.includes(item.id);
    const isActive = location.pathname === item.path;

    const hasActiveChild =
      hasChildren &&
      item.children?.some((child) => location.pathname === child.path);

    if (hasChildren) {
      return (
        <div key={item.id}>
          <button
            onClick={() => toggleExpanded(item.id)}
            className={`w-full flex items-center justify-between px-4 py-3 text-left transition-colors duration-200 ${
              hasActiveChild
                ? 'bg-gray-700 text-white'
                : 'text-gray-300 hover:bg-gray-700 hover:text-white'
            } ${level > 0 ? 'pl-8' : ''}`}
          >
            <div className='flex items-center space-x-3'>
              {item.icon}
              <span className='font-medium'>{item.label}</span>
            </div>
            {isExpanded ? (
              <ChevronDown className='w-4 h-4' />
            ) : (
              <ChevronRight className='w-4 h-4' />
            )}
          </button>
          {isExpanded && (
            <div className='bg-gray-700'>
              {item.children?.map((child) => renderMenuItem(child, level + 1))}
            </div>
          )}
        </div>
      );
    }

    return (
      <Link
        key={item.id}
        to={item.path}
        onClick={() => {
          if (window.innerWidth < 768) {
            onClose();
          }
        }}
        className={`w-full flex items-center justify-between px-4 py-3 text-left transition-colors duration-200 ${
          isActive
            ? 'bg-blue-600 text-white'
            : 'text-gray-300 hover:bg-gray-700 hover:text-white'
        } ${level > 0 ? 'pl-8' : ''}`}
      >
        <div className='flex items-center space-x-3'>
          {item.icon}
          <span className='font-medium'>{item.label}</span>
        </div>
      </Link>
    );
  };

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div
          className='fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden'
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <div
        className={`
        fixed top-0 left-0 h-full w-64 bg-gray-800 text-white transform transition-transform duration-300 ease-in-out z-50
        ${isOpen ? 'translate-x-0' : '-translate-x-full'}
        md:relative md:translate-x-0
      `}
      >
        <div className='flex items-center justify-between p-4 border-b border-gray-700'>
          <h2 className='text-xl font-bold'>EHR Admin</h2>
          <button
            onClick={onClose}
            className='md:hidden text-gray-300 hover:text-white'
          >
            ×
          </button>
        </div>

        <nav className='flex-1 overflow-y-auto'>
          <div className='py-4'>
            {menuItems.map((item) => renderMenuItem(item))}
          </div>
        </nav>
      </div>
    </>
  );
};

export default Sidebar;
