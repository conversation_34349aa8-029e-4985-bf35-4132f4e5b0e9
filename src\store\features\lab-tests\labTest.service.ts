import { LAB_TEST_ENDPOINT } from '../../../constants/api-endpoints';
import { BulkUpdateStatus } from '../../../features/lab-tests/types/bulk-update.types';
import {
  LabTestListItem,
  LabTestListParams,
  LabTestListResponse,
  UpdateLabTestRequest,
} from '../../../features/lab-tests/types/labTest.types';
import api from '../../../services/api';

// API endpoints
const LAB_TEST_ENDPOINTS = {
  LIST: `${LAB_TEST_ENDPOINT}/loinc/list`,
  UPDATE: `${LAB_TEST_ENDPOINT}/loinc/update`,
  REMOVE: `${LAB_TEST_ENDPOINT}/loinc/remove`,
  EXPORT: `${LAB_TEST_ENDPOINT}/loinc/export`,
  STATUS: `${LAB_TEST_ENDPOINT}/loinc/status`,
} as const;

/**
 * Fetch lab tests list with search and filtering
 */
const fetchLabTestsList = async (
  params: LabTestListParams
): Promise<LabTestListResponse> => {
  const queryParams = new URLSearchParams();

  if (params.searchText) {
    queryParams.append('searchText', params.searchText);
  }

  if (params.department && params.department !== 'all') {
    queryParams.append('department', params.department);
  }

  if (params.pageSize) {
    queryParams.append('pageSize', params.pageSize.toString());
  }

  if (params.page) {
    queryParams.append('page', params.page.toString());
  }

  if (params.organizationId) {
    queryParams.append('organizationId', params.organizationId);
  }

  if (params.isActive !== undefined) {
    queryParams.append('isActive', params.isActive.toString());
  }

  if (params.continuationToken) {
    queryParams.append('continuationToken', params.continuationToken);
  }

  const response = await api.get<{
    items: any[];
    continuationToken?: string;
    currentPage: number;
    hasMoreResults?: boolean;
    pageSize?: number;
    totalCount?: number;
    totalFetched?: number;
    totalPages?: number;
  }>(`${LAB_TEST_ENDPOINTS.LIST}?${queryParams.toString()}`);

  const mappedTests: LabTestListItem[] =
    response.data.items?.map((item: any, index: number) => ({
      id: item.loincNum || `lab-test-${index}`,
      testId: item.loincNum,
      testName: item.longCommonName || item.displayName || item.shortName,
      shortName: item.shortName,
      defaultCost: item.cost || 0,
      description: item.longCommonName || '',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isActive: item.isActive !== undefined ? item.isActive : true,
      organizationPrice: item.organizationCost || 0,
      departments: [],
      class: item?.class || '',
      organizationLabTestId: item.id,
    })) || [];

  return {
    tests: mappedTests,
    totalRecords: response.data.totalCount || 0,
    currentPage: response.data.currentPage || 1,
    totalPages: response.data.totalPages || 1,
    continuationToken: response.data.continuationToken ?? undefined,
    hasMoreResults: response.data.hasMoreResults ?? undefined,
    pageSize: response.data.pageSize ?? undefined,
    totalFetched: response.data.totalFetched ?? undefined,
  };
};

interface BulkUpdateStartResponse {
  async: boolean;
  jobId: string;
  message: string;
  statusUrl: string;
}

interface StandardUpdateResponse {
  success: boolean;
  message: string;
}

/**
 * Update organization-specific lab test configuration
 */
const updateLabTests = async (
  data: UpdateLabTestRequest
): Promise<StandardUpdateResponse | BulkUpdateStartResponse> => {
  const response = await api.post<
    StandardUpdateResponse | BulkUpdateStartResponse
  >(LAB_TEST_ENDPOINTS.UPDATE, data);

  return response.data;
};

/**
 * Check bulk update status
 * @param statusUrl - The status URL path (e.g., 'loinc/update/status/cc4a57c9-c262-4da0-9b77-dff5bba346e2')
 * @returns The current status of the bulk update operation
 * @throws {AxiosError} If the request fails
 */
const checkBulkUpdateStatus = async (
  statusUrl: string
): Promise<BulkUpdateStatus> => {
  try {
    // Remove any leading slashes to prevent double slashes
    const cleanStatusUrl = statusUrl.startsWith('/')
      ? statusUrl.slice(1)
      : statusUrl;

    // Construct the full URL using the base API URL
    const fullUrl = `${LAB_TEST_ENDPOINT}/${cleanStatusUrl}`;

    const response = await api.get<BulkUpdateStatus>(fullUrl);
    return response.data;
  } catch (error: any) {
    // Re-throw the error with the response data if available
    if (error.response) {
      throw {
        ...error,
        response: {
          ...error.response,
          data: {
            message:
              typeof error.response.data === 'string'
                ? error.response.data
                : error.response.data?.message ||
                  'Failed to check update status',
            ...(typeof error.response.data === 'object' &&
            error.response.data !== null
              ? error.response.data
              : {}),
          },
        },
      };
    }
    throw error;
  }
};

/**
 * Export lab tests list to Excel
 */
const exportLabTestsList = async (params: LabTestListParams): Promise<Blob> => {
  const queryParams = new URLSearchParams();

  if (params.searchText) {
    queryParams.append('searchText', params.searchText);
  }

  if (params.department && params.department !== 'all') {
    queryParams.append('department', params.department);
  }

  if (params.organizationId) {
    queryParams.append('organizationId', params.organizationId);
  }

  if (params.isActive !== undefined) {
    queryParams.append('isActive', params.isActive.toString());
  }

  const response = await api.get(
    `${LAB_TEST_ENDPOINTS.EXPORT}?${queryParams.toString()}`,
    {
      responseType: 'blob',
    }
  );

  return response.data;
};

/**
 * Download exported file
 */
const downloadExportedFile = (
  blob: Blob,
  filename: string = 'lab-tests-list.xlsx'
) => {
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  window.URL.revokeObjectURL(url);
};

/**
 * Remove lab tests from organization
 */
const removeLabTests = async (data: {
  organizationId: string;
  tests?: string[];
  selectAll?: boolean;
  department?: string;
}): Promise<StandardUpdateResponse | BulkUpdateStartResponse> => {
  const response = await api.post<
    StandardUpdateResponse | BulkUpdateStartResponse
  >(LAB_TEST_ENDPOINTS.REMOVE, data);
  return response.data;
};

// Export all service functions
export const labTestService = {
  fetchLabTestsList,
  updateLabTests,
  removeLabTests,
  checkBulkUpdateStatus,
  exportLabTests: exportLabTestsList,
  downloadExportedFile,
};

export default labTestService;
