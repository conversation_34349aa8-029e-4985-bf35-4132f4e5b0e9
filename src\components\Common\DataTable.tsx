import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import Paper from '@mui/material/Paper';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import { Edit, Eye, Filter, MoreVertical, Trash2 } from 'lucide-react';
import React, { memo, useCallback, useMemo, useState } from 'react';

import LoadingSpinner from './LoadingSpinner';
import Pagination from './Pagination';

export interface Column<T> {
  key: keyof T | 'actions';
  label: string | React.ReactNode;
  render?: (value: any, item: T) => React.ReactNode;
  sortable?: boolean;
  width?: string;
}

export interface DataTableProps<T extends { id: string }> {
  data: T[];
  columns: Column<T>[];
  loading?: boolean;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    onPageChange: (page: number) => void;
  };
  filterable?: boolean;
  filters?: React.ReactNode;
  searchFilters?: React.ReactNode;
  onView?: (item: T) => void;
  onEdit?: (item: T) => void;
  onDelete?: (item: T) => void;
  actions?:
    | Array<{
        label: string;
        icon?: React.ReactNode;
        onClick: (item: T) => void;
        color?: 'primary' | 'secondary' | 'danger';
        disabled?: boolean;
      }>
    | ((item: T) => Array<{
        label: string;
        icon?: React.ReactNode;
        onClick: (item: T) => void;
        color?: 'primary' | 'secondary' | 'danger';
        disabled?: boolean;
      }>);
  // Selection props
  selectedIds?: string[];
  isAllSelected?: boolean;
  onSelectAll?: (selected: boolean) => void;
  onSelectOne?: (id: string, selected: boolean) => void;
}

const DataTable = <T extends { id: string }>({
  data,
  columns,
  loading = false,
  pagination,
  filterable = false,
  filters,
  searchFilters,
  onView,
  onEdit,
  onDelete,
  actions,
  selectedIds = [],
  isAllSelected = false,
  onSelectAll,
  onSelectOne,
}: DataTableProps<T>) => {
  const [showFilters, setShowFilters] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [menuItemId, setMenuItemId] = useState<string | null>(null);

  // Get actions for a specific item
  const getActionsForItem = useCallback(
    (item: T) => {
      const baseActions = [
        ...(onView
          ? [
              {
                label: 'View',
                icon: <Eye className='w-4 h-4' />,
                onClick: onView,
                disabled: false,
              },
            ]
          : []),
        ...(onEdit
          ? [
              {
                label: 'Edit',
                icon: <Edit className='w-4 h-4' />,
                onClick: onEdit,
                disabled: false,
              },
            ]
          : []),
        ...(onDelete
          ? [
              {
                label: 'Delete',
                icon: <Trash2 className='w-4 h-4' />,
                onClick: onDelete,
                color: 'danger' as const,
                disabled: false,
              },
            ]
          : []),
      ];

      if (actions) {
        if (typeof actions === 'function') {
          return [...baseActions, ...actions(item)];
        } else {
          return [...baseActions, ...actions];
        }
      }

      return baseActions;
    },
    [onView, onEdit, onDelete, actions]
  );

  const hasActions = useMemo(() => {
    if (onView || onEdit || onDelete || actions) {
      return true;
    }
    return false;
  }, [onView, onEdit, onDelete, actions]);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, id: string) => {
    setAnchorEl(event.currentTarget);
    setMenuItemId(id);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setMenuItemId(null);
  };

  // Calculate total fixed width and number of flexible columns
  const fixedWidths = columns
    .map((col) => col.width)
    .filter(Boolean)
    .map((w) => (typeof w === 'string' && w.endsWith('px') ? parseInt(w) : 0));
  const totalFixedPx = fixedWidths.reduce((a, b) => a + b, 0);
  const numFlexibleCols = columns.filter((col) => !col.width).length;
  const flexibleColWidth =
    numFlexibleCols > 0
      ? `calc((100% - ${totalFixedPx}px) / ${numFlexibleCols})`
      : undefined;

  return (
    <div className='bg-white rounded-lg shadow overflow-hidden'>
      {/* Filters area */}
      {(searchFilters || filterable) && (
        <div className='p-4 border-b border-gray-200'>
          <div className='space-y-3'>
            {/* Inline filters - displayed in organized rows */}
            {searchFilters && (
              <div className='flex flex-wrap gap-3 items-center'>
                {searchFilters}
              </div>
            )}

            {/* Advanced filters toggle and content */}
            {filterable && (
              <div className='space-y-3'>
                <div className='flex justify-start'>
                  <button
                    onClick={() => setShowFilters(!showFilters)}
                    className='inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50'
                  >
                    <Filter className='w-4 h-4 mr-2' />
                    Advanced Filters
                  </button>
                </div>
                {showFilters && (
                  <div className='p-4 bg-gray-50 rounded-lg'>{filters}</div>
                )}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Table */}
      <div className='overflow-x-auto'>
        <TableContainer component={Paper} sx={{ boxShadow: 'none' }}>
          <Table size='small'>
            <TableHead>
              <TableRow>
                {/* Checkbox for select all */}
                {onSelectAll && (
                  <TableCell padding='checkbox'>
                    <input
                      type='checkbox'
                      checked={isAllSelected}
                      onChange={(e) => onSelectAll(e.target.checked)}
                      className='h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500'
                    />
                  </TableCell>
                )}
                {columns.map((col) => (
                  <TableCell
                    key={String(col.key)}
                    style={{ width: col.width || flexibleColWidth }}
                    sx={{
                      fontWeight: 400,
                      color: '#1a2233',
                      backgroundColor: 'white',
                      fontSize: '0.95rem',
                    }}
                  >
                    {col.label}
                  </TableCell>
                ))}
                {hasActions && (
                  <TableCell
                    align='right'
                    style={{ width: 80 }}
                    sx={{
                      fontWeight: 400,
                      color: '#1a2233',
                      backgroundColor: 'white',
                      fontSize: '0.95rem',
                    }}
                  >
                    Actions
                  </TableCell>
                )}
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell
                    colSpan={
                      columns.length +
                      (hasActions ? 1 : 0) +
                      (onSelectAll ? 1 : 0)
                    }
                  >
                    <div className='flex justify-center py-8 text-gray-500'>
                      <LoadingSpinner size='md' />
                    </div>
                  </TableCell>
                </TableRow>
              ) : data.length === 0 ? (
                <TableRow>
                  <TableCell
                    colSpan={
                      columns.length +
                      (hasActions ? 1 : 0) +
                      (onSelectAll ? 1 : 0)
                    }
                  >
                    <div className='flex justify-center py-8 text-gray-500'>
                      No data found.
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                data.map((item) => (
                  <TableRow key={item.id}>
                    {/* Checkbox for row selection */}
                    {onSelectOne && (
                      <TableCell padding='checkbox'>
                        <input
                          type='checkbox'
                          checked={
                            isAllSelected || selectedIds.includes(item.id)
                          }
                          onChange={(e) =>
                            onSelectOne(item.id, e.target.checked)
                          }
                          disabled={isAllSelected}
                          className={`h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 ${
                            isAllSelected
                              ? 'opacity-50 cursor-not-allowed'
                              : 'cursor-pointer'
                          }`}
                        />
                      </TableCell>
                    )}
                    {columns.map((col) => (
                      <TableCell
                        key={String(col.key)}
                        style={{ width: col.width || flexibleColWidth }}
                      >
                        {col.render
                          ? col.render(item[col.key as keyof typeof item], item)
                          : String(item[col.key as keyof typeof item] ?? '')}
                      </TableCell>
                    ))}
                    {hasActions && (
                      <TableCell align='right' style={{ width: 80 }}>
                        <div>
                          <button
                            onClick={(e) => handleMenuOpen(e, item.id)}
                            className='text-gray-400 hover:text-gray-600'
                          >
                            <MoreVertical className='w-4 h-4' />
                          </button>
                          <Menu
                            anchorEl={anchorEl}
                            open={menuItemId === item.id}
                            onClose={handleMenuClose}
                            anchorOrigin={{
                              vertical: 'bottom',
                              horizontal: 'right',
                            }}
                            transformOrigin={{
                              vertical: 'top',
                              horizontal: 'right',
                            }}
                            PaperProps={{
                              sx: { minWidth: 180, boxShadow: 3 },
                            }}
                          >
                            {getActionsForItem(item).map(
                              (action: any, actionIndex: number) => (
                                <MenuItem
                                  key={actionIndex}
                                  disabled={action.disabled}
                                  onClick={() => {
                                    if (!action.disabled) {
                                      action.onClick(item);
                                      handleMenuClose();
                                    }
                                  }}
                                  sx={{
                                    ...(action.color === 'danger' &&
                                    !action.disabled
                                      ? { color: 'error.main' }
                                      : {}),
                                    ...(action.disabled
                                      ? {
                                          color: 'text.disabled',
                                          cursor: 'not-allowed',
                                        }
                                      : {}),
                                  }}
                                >
                                  {action.icon && (
                                    <ListItemIcon
                                      sx={{
                                        ...(action.color === 'danger' &&
                                        !action.disabled
                                          ? { color: 'error.main' }
                                          : {}),
                                        ...(action.disabled
                                          ? { color: 'text.disabled' }
                                          : {}),
                                      }}
                                    >
                                      {action.icon}
                                    </ListItemIcon>
                                  )}
                                  <ListItemText>{action.label}</ListItemText>
                                </MenuItem>
                              )
                            )}
                          </Menu>
                        </div>
                      </TableCell>
                    )}
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </div>

      {pagination && (
        <Pagination
          page={pagination.page}
          limit={pagination.limit}
          total={pagination.total}
          onPageChange={(_, newPage) => pagination.onPageChange(newPage)}
        />
      )}
    </div>
  );
};

// Memoize the component to prevent unnecessary re-renders
export default memo(DataTable) as <T extends { id: string }>(
  props: DataTableProps<T>
) => JSX.Element;
