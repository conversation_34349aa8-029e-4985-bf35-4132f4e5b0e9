import { yupResolver } from '@hookform/resolvers/yup';
import React, { useCallback, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';

import Modal from '../../../components/Common/Modal';
import { DEPARTMENT_OPTIONS, LabTestListItem } from '../types/labTest.types';

// Form schema
const labTestEditSchema = yup.object({
  organizationCost: yup
    .number()
    .min(0, 'Cost must be non-negative')
    .required('Organization cost is required'),
  departments: yup
    .array()
    .of(yup.string().required())
    .min(1, 'Please select at least one department')
    .required('Departments are required'),
});

type LabTestEditFormData = yup.InferType<typeof labTestEditSchema>;

interface LabTestEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  test: LabTestListItem | null;
  onSave: (testId: string, data: LabTestEditFormData) => void;
}

const LabTestEditModal: React.FC<LabTestEditModalProps> = ({
  isOpen,
  onClose,
  test,
  onSave,
}) => {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    setValue,
    watch,
  } = useForm<LabTestEditFormData>({
    resolver: yupResolver(labTestEditSchema),
    defaultValues: {
      organizationCost: 0,
      departments: [],
    },
  });

  const selectedDepartments = watch('departments');

  // Reset form when test changes
  useEffect(() => {
    if (test) {
      reset({
        organizationCost: test.organizationCost || test.organizationPrice || 0,
        departments: (test.departments as string[]) || [],
      });
    }
  }, [test, reset]);

  const onSubmit = useCallback(
    (formData: LabTestEditFormData) => {
      if (!test) return;

      // Prepare the data to be saved
      const saveData = {
        ...formData,
        // Ensure we have at least one department
        departments: formData.departments?.length ? formData.departments : [],
        // For backward compatibility, include the first department as department
        department: formData.departments?.[0] || '',
      };

      onSave(test.testId, saveData);
      onClose();
    },
    [test, onSave, onClose]
  );

  if (!test) return null;

  return (
    <Modal isOpen={isOpen} onClose={onClose} title='Edit Lab Test' size='lg'>
      <form onSubmit={handleSubmit(onSubmit)} className='space-y-6'>
        {/* Test Name - Disabled Input */}
        <div>
          <label className='block text-sm font-medium text-gray-700 mb-2'>
            Test Name
          </label>
          <input
            type='text'
            value={test.testName}
            disabled
            className='block w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-500 cursor-not-allowed'
          />
        </div>

        {/* Organization Cost */}
        <div>
          <label
            htmlFor='organizationCost'
            className='block text-sm font-medium text-gray-700'
          >
            Organization Cost *
          </label>
          <div className='mt-1 relative'>
            <span className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500'>
              ₹
            </span>
            <input
              type='number'
              step='0.01'
              min='0'
              id='organizationCost'
              {...register('organizationCost')}
              className={`pl-8 block w-full rounded-lg border ${
                errors.organizationCost ? 'border-red-300' : 'border-gray-300'
              } px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
              placeholder='0.00'
            />
          </div>
          {errors.organizationCost && (
            <p className='mt-1 text-sm text-red-600'>
              {errors.organizationCost.message}
            </p>
          )}
        </div>

        {/* Departments */}
        <div>
          <label className='block text-sm font-medium text-gray-700 mb-2'>
            Departments *
          </label>
          <div className='grid grid-cols-2 gap-2 max-h-48 overflow-y-auto border border-gray-300 rounded-lg p-3'>
            {DEPARTMENT_OPTIONS.map((department) => (
              <label key={department} className='flex items-center'>
                <input
                  type='checkbox'
                  checked={selectedDepartments?.includes(department) || false}
                  onChange={(e) => {
                    const currentDepartments = selectedDepartments || [];
                    if (e.target.checked) {
                      setValue('departments', [
                        ...currentDepartments,
                        department,
                      ] as string[]);
                    } else {
                      setValue(
                        'departments',
                        currentDepartments.filter(
                          (d) => d !== department
                        ) as string[]
                      );
                    }
                  }}
                  className='h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded'
                />
                <span className='ml-2 text-sm text-gray-700'>{department}</span>
              </label>
            ))}
          </div>
          {errors.departments && (
            <p className='mt-1 text-sm text-red-600'>
              {errors.departments.message}
            </p>
          )}
        </div>

        {/* Form Actions */}
        <div className='flex justify-end space-x-3 pt-6 border-t border-gray-200'>
          <button
            type='button'
            onClick={onClose}
            className='px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
          >
            Cancel
          </button>
          <button
            type='submit'
            disabled={isSubmitting}
            className='px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed'
          >
            {isSubmitting ? 'Saving...' : 'Save Changes'}
          </button>
        </div>
      </form>
    </Modal>
  );
};

export default LabTestEditModal;
