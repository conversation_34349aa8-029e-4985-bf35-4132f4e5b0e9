import {
  Box,
  Button,
  Checkbox,
  Collapse,
  FormControlLabel,
  FormGroup,
  Typography,
} from '@mui/material';
import { ShieldCheck } from 'lucide-react';
import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';

import LoadingSpinner from '../../../components/Common/LoadingSpinner';
import { permissionKeys } from '../../../constants/permission-keys';
import { ModuleType } from '../../../types';
import { usePermissions } from '../hooks/usePermissions';

interface RolePermissionManagerProps {
  roleId: string;
  roleName?: string;
  onClose: () => void;
}

interface Feature {
  name: string;
  label: string;
  keys: string[];
  subFeatures?: string[];
  departmentPackageKeys?: string[];
  queeKeys?: string[];
}

const allModules: { name: ModuleType; features: Feature[] }[] = [
  {
    name: 'MRD',
    features: [
      {
        name: 'Manage Patient',
        label: 'Manage Patient',
        keys: [
          permissionKeys.mrd.patientManagement.view,
          permissionKeys.mrd.patientManagement.edit,
        ],
        subFeatures: ['Patient Queue'],
        queeKeys: [permissionKeys.mrd.patientQueue.manage],
      },
    ],
  },
  {
    name: 'EMR',
    features: [
      {
        name: 'Patient Info',
        label: 'Patient Information',
        keys: [
          permissionKeys.emr.patientInfo.view,
          permissionKeys.emr.patientInfo.edit,
        ],
      },
      {
        name: 'Consultation',
        label: 'Consultation',
        keys: [permissionKeys.emr.consultation.manage],
      },
      {
        name: 'Prescription',
        label: 'Prescriptions',
        keys: [
          permissionKeys.emr.prescriptions.view,
          permissionKeys.emr.prescriptions.manage,
        ],
        subFeatures: ['Department Package'],
        departmentPackageKeys: [permissionKeys.emr.medicinePackage.manage],
      },
      {
        name: 'Lab Tests',
        label: 'Lab Tests',
        keys: [permissionKeys.emr.reports.manage],
        subFeatures: ['Department Package'],
        departmentPackageKeys: [permissionKeys.emr.testPackage.manage],
      },
      {
        name: 'Doctor Profile',
        label: 'Doctor Profile',
        keys: [
          permissionKeys.emr.doctorProfile.view,
          permissionKeys.emr.doctorProfile.edit,
        ],
      },
    ],
  },
];

// Helper function to get module access key based on feature keys
const getModuleAccessKey = (featureKeys: string[]): string | null => {
  if (featureKeys.some((key) => key.startsWith('emr.'))) {
    return permissionKeys.emr.access;
  }
  if (featureKeys.some((key) => key.startsWith('mrd.'))) {
    return permissionKeys.mrd.access;
  }
  return null;
};
// Helper function to check if a module checkbox should be checked (if it has any selected features)
const isModuleChecked = (
  moduleName: string,
  selectedKeys: string[]
): boolean => {
  const modulePrefix = moduleName.toLowerCase();
  return selectedKeys.some((key) => key.startsWith(`${modulePrefix}.`));
};

const RolePermissionManager: React.FC<RolePermissionManagerProps> = memo(
  ({ roleId, onClose }) => {
    const {
      rolePermissions,
      loading,
      assignLoading,
      assignSuccess,
      fetchRolePermissions,
      updatePermissions,
      clearMessages,
      clearRolePermissions,
    } = usePermissions();

    const [selectedPermissionKeys, setSelectedPermissionKeys] = useState<
      string[]
    >([]);
    const [openModules, setOpenModules] = useState<ModuleType[]>(['EMR']); // EMR module starts open

    // Fetch role permissions on mount and clear previous state
    useEffect(() => {
      if (roleId) {
        // Clear previous permissions first
        clearRolePermissions();
        setSelectedPermissionKeys([]);
        fetchRolePermissions(roleId);
      }
    }, [roleId, fetchRolePermissions, clearRolePermissions]);

    // Update selected permission keys and open modules when role permissions change
    useEffect(() => {
      // Extract permission keys from role permissions (only those with valid keys)
      const keys: string[] = rolePermissions
        .filter((permission) => permission.key && permission.key !== null)
        .map((permission) => permission.key!)
        .filter((key): key is string => key !== undefined);

      // Also check for module access permissions based on existing permissions
      const hasEmrPermissions = rolePermissions.some((permission) =>
        permission.key?.startsWith('emr.')
      );
      const hasMrdPermissions = rolePermissions.some((permission) =>
        permission.key?.startsWith('mrd.')
      );

      // Add module access permissions if there are any module-specific permissions
      if (hasEmrPermissions && !keys.includes(permissionKeys.emr.access)) {
        keys.push(permissionKeys.emr.access);
      }
      if (hasMrdPermissions && !keys.includes(permissionKeys.mrd.access)) {
        keys.push(permissionKeys.mrd.access);
      }

      setSelectedPermissionKeys(Array.from(new Set(keys))); // Remove duplicates

      // --- NEW: Expand modules that are checked ---
      const open: ModuleType[] = [];
      if (
        keys.some((key) => key.startsWith('emr.')) ||
        keys.includes(permissionKeys.emr.access)
      ) {
        open.push('EMR');
      }
      if (
        keys.some((key) => key.startsWith('mrd.')) ||
        keys.includes(permissionKeys.mrd.access)
      ) {
        open.push('MRD');
      }
      setOpenModules(open);
    }, [rolePermissions]);

    // Clear messages and role permissions on unmount
    useEffect(() => {
      return () => {
        clearMessages();
        clearRolePermissions();
        setSelectedPermissionKeys([]);
      };
    }, [clearMessages, clearRolePermissions]);

    // Memoize permission lookup for performance
    const permissionMap = useMemo(() => {
      const map = new Map<string, boolean>();
      selectedPermissionKeys.forEach((key) => {
        map.set(key, true);
      });
      return map;
    }, [selectedPermissionKeys]);

    // Handle module checkbox (parent) toggle
    const handleModuleToggle = useCallback(
      (moduleName: ModuleType) => {
        const module = allModules.find((m) => m.name === moduleName);
        if (!module) return;
        const moduleAccessKey =
          moduleName === 'EMR'
            ? permissionKeys.emr.access
            : moduleName === 'MRD'
              ? permissionKeys.mrd.access
              : null;
        let newKeys = [...selectedPermissionKeys];
        const isChecked = isModuleChecked(moduleName, selectedPermissionKeys);

        if (isChecked) {
          // Uncheck: remove module access key and all child feature keys
          if (moduleAccessKey) {
            newKeys = newKeys.filter((key) => key !== moduleAccessKey);
          }
          // Remove all child feature keys and department package keys
          module.features.forEach((feature) => {
            newKeys = newKeys.filter((key) => !feature.keys.includes(key));
            if (feature.departmentPackageKeys) {
              newKeys = newKeys.filter(
                (key) => !feature.departmentPackageKeys?.includes(key)
              );
            }
          });
        } else {
          // Check: add module access key only
          if (moduleAccessKey && !newKeys.includes(moduleAccessKey)) {
            newKeys.push(moduleAccessKey);
          }
        }
        setSelectedPermissionKeys(newKeys);
        // Expand module if checked
        setOpenModules((prev) =>
          isChecked
            ? prev.filter((m) => m !== moduleName)
            : [...prev, moduleName]
        );
      },
      [selectedPermissionKeys]
    );

    const handleFeatureChange = useCallback(
      (feature: Feature, checked: boolean) => {
        let newKeys = [...selectedPermissionKeys];

        if (checked) {
          // Add all permission keys for this feature
          feature.keys.forEach((key: string) => {
            if (!newKeys.includes(key)) {
              newKeys.push(key);
            }
          });

          // Automatically add module access permission based on the feature keys
          const moduleAccessKey = getModuleAccessKey(feature.keys);
          if (moduleAccessKey && !newKeys.includes(moduleAccessKey)) {
            newKeys.push(moduleAccessKey);
          }
        } else {
          // Remove all permission keys for this feature and department package keys if they exist
          newKeys = newKeys.filter((key) => !feature.keys.includes(key));
          if (feature.departmentPackageKeys) {
            newKeys = newKeys.filter(
              (key) => !feature.departmentPackageKeys?.includes(key)
            );
          }
        }

        setSelectedPermissionKeys(newKeys);
      },
      [selectedPermissionKeys]
    );

    const handleDepartmentPackageChange = useCallback(
      (feature: Feature, checked: boolean) => {
        if (!feature.departmentPackageKeys) return;

        let newKeys = [...selectedPermissionKeys];

        if (checked) {
          // First ensure parent feature permissions are checked
          feature.keys.forEach((key: string) => {
            if (!newKeys.includes(key)) {
              newKeys.push(key);
            }
          });
          // Then add department package permissions
          feature.departmentPackageKeys.forEach((key: string) => {
            if (!newKeys.includes(key)) {
              newKeys.push(key);
            }
          });
        } else {
          // Only remove department package permissions
          newKeys = newKeys.filter(
            (key) => !feature.departmentPackageKeys?.includes(key)
          );
        }

        setSelectedPermissionKeys(newKeys);
      },
      [selectedPermissionKeys]
    );

    const handlePatientQueueChange = useCallback(
      (feature: Feature, checked: boolean) => {
        if (!feature.queeKeys) return;

        let newKeys = [...selectedPermissionKeys];

        if (checked) {
          feature.keys.forEach((key: string) => {
            if (!newKeys.includes(key)) {
              newKeys.push(key);
            }
          });
          feature.queeKeys.forEach((key: string) => {
            if (!newKeys.includes(key)) {
              newKeys.push(key);
            }
          });
        } else {
          newKeys = newKeys.filter((key) => !feature.queeKeys?.includes(key));
        }

        setSelectedPermissionKeys(newKeys);
      },
      [selectedPermissionKeys]
    );

    const isPatientQueueChecked = useCallback(
      (feature: Feature) => {
        if (!feature.queeKeys) return false;
        return feature.queeKeys.every((key: string) => permissionMap.has(key));
      },
      [permissionMap]
    );

    const isDepartmentPackageChecked = useCallback(
      (feature: Feature) => {
        if (!feature.departmentPackageKeys) return false;
        return feature.departmentPackageKeys.every((key: string) =>
          permissionMap.has(key)
        );
      },
      [permissionMap]
    );

    const isFeatureChecked = useCallback(
      (feature: Feature) => {
        return feature.keys.every((key) => permissionMap.has(key));
      },
      [permissionMap]
    );

    // Handle save permissions
    const handleSavePermissions = useCallback(async () => {
      try {
        // Filter out any invalid permission keys and only send valid ones
        const validPermissionKeys = selectedPermissionKeys.filter(
          (key) => key && key.trim() !== ''
        );

        await updatePermissions(roleId, validPermissionKeys);
      } catch {
        // Error is handled by the Redux slice
      }
    }, [roleId, selectedPermissionKeys, updatePermissions]);

    // Close modal on successful save
    useEffect(() => {
      if (assignSuccess) {
        // Clear success state and close modal
        clearMessages();
        onClose();
      }
    }, [assignSuccess, clearMessages, onClose]);

    // Show loading spinner when fetching permissions
    if (loading) {
      return (
        <Box
          display='flex'
          justifyContent='center'
          alignItems='center'
          minHeight='60vh'
          sx={{ p: 2 }}
        >
          <LoadingSpinner size='md' text='Loading permissions...' />
        </Box>
      );
    }

    return (
      <Box
        sx={{ display: 'flex', flexDirection: 'column', height: '60vh', p: 2 }}
      >
        <Box sx={{ flex: 1, overflowY: 'auto', pr: 1, mb: 2 }}>
          {allModules.map((module) => {
            const isModuleOpen = openModules.includes(module.name);

            return (
              <Box key={module.name} sx={{ mb: 2 }}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={isModuleChecked(
                        module.name,
                        selectedPermissionKeys
                      )}
                      onChange={() => handleModuleToggle(module.name)}
                      disabled={assignLoading}
                    />
                  }
                  label={<Typography variant='h6'>{module.name}</Typography>}
                />
                <Collapse in={isModuleOpen}>
                  <Box sx={{ display: 'flex', flexDirection: 'column', ml: 3 }}>
                    <FormGroup>
                      {module.features.map((feature) => (
                        <Box key={feature.name}>
                          <FormControlLabel
                            control={
                              <Checkbox
                                checked={isFeatureChecked(feature)}
                                onChange={(e) =>
                                  handleFeatureChange(feature, e.target.checked)
                                }
                                disabled={assignLoading}
                              />
                            }
                            label={feature.label}
                          />
                          {/* Department Package subfeature */}
                          {feature.subFeatures?.includes(
                            'Department Package'
                          ) &&
                            isFeatureChecked(feature) && (
                              <Box sx={{ ml: 4 }}>
                                <FormControlLabel
                                  control={
                                    <Checkbox
                                      checked={isDepartmentPackageChecked(
                                        feature
                                      )}
                                      onChange={(e) =>
                                        handleDepartmentPackageChange(
                                          feature,
                                          e.target.checked
                                        )
                                      }
                                      disabled={
                                        !isFeatureChecked(feature) ||
                                        assignLoading
                                      }
                                    />
                                  }
                                  label='Department Package'
                                />
                              </Box>
                            )}
                          {feature.subFeatures?.includes('Patient Queue') &&
                            isFeatureChecked(feature) && (
                              <Box sx={{ ml: 4 }}>
                                <FormControlLabel
                                  control={
                                    <Checkbox
                                      checked={isPatientQueueChecked(feature)}
                                      onChange={(e) =>
                                        handlePatientQueueChange(
                                          feature,
                                          e.target.checked
                                        )
                                      }
                                      disabled={
                                        !isFeatureChecked(feature) ||
                                        assignLoading
                                      }
                                    />
                                  }
                                  label='Patient Queue'
                                />
                              </Box>
                            )}
                        </Box>
                      ))}
                    </FormGroup>
                  </Box>
                </Collapse>
              </Box>
            );
          })}
        </Box>

        <Box
          sx={{
            display: 'flex',
            justifyContent: 'flex-end',
            gap: 2,
            pt: 2,
            pb: 2,
            px: 3,
            borderTop: '1px solid',
            borderColor: 'divider',
            flexShrink: 0,
          }}
        >
          <Button variant='outlined' onClick={onClose} disabled={assignLoading}>
            CANCEL
          </Button>
          <Button
            variant='contained'
            onClick={handleSavePermissions}
            disabled={assignLoading}
            startIcon={<ShieldCheck className='w-4 h-4' />}
          >
            {assignLoading ? 'Saving...' : 'SAVE PERMISSIONS'}
          </Button>
        </Box>
      </Box>
    );
  }
);

RolePermissionManager.displayName = 'RolePermissionManager';

export default RolePermissionManager;
