/**
 * Calculate age from date of birth (YYYY-MM-DD format)
 * @param dobString - Date of birth in YYYY-MM-DD format
 * @returns Age in years as a number
 */
export const calculateAge = (dobString: string): number => {
  if (!dobString) return 0;

  const today = new Date();
  const birthDate = new Date(dobString);

  // Check if the date is valid
  if (isNaN(birthDate.getTime())) return 0;

  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();

  // If birthday hasn't occurred yet this year, subtract 1 from age
  if (
    monthDiff < 0 ||
    (monthDiff === 0 && today.getDate() < birthDate.getDate())
  ) {
    age--;
  }

  return age < 0 ? 0 : age; // Return 0 if DOB is in the future
};
