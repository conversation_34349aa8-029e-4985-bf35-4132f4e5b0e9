import { isFulfilled } from '@reduxjs/toolkit';
import { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { AppDispatch, RootState } from '../../../store';
import {
  checkBulkUpdateStatus,
  stopBulkUpdatePolling,
  updateBulkUpdateStatus,
} from '../../../store/features/lab-tests/labTest.slice';
import { useLabTests } from '../hooks/useLabTests';
import { getDepartmentFilterValue } from '../services/department.service';
import { BulkUpdateStatus } from '../types/bulk-update.types';
import { BulkUpdateStatus as BulkUpdateStatusComponent } from './BulkUpdateStatus';

interface BulkUpdateStatusIndicatorProps {
  show?: boolean;
}

export const BulkUpdateStatusIndicator: React.FC<
  BulkUpdateStatusIndicatorProps
> = ({ show = true }) => {
  // Move all hooks to the top, before any conditional logic
  const { status: reduxStatus, isPolling: isReduxPolling } = useSelector(
    (state: RootState) => state.labTests.bulkUpdate
  );

  // Get organizationId from the auth state
  const organizationId = useSelector(
    (state: RootState) => state.auth.selectedOrganization?.id
  );

  const { fetchLabTests } = useLabTests(organizationId);
  const dispatch = useDispatch<AppDispatch>();

  const [localStatus, setLocalStatus] = useState<BulkUpdateStatus | null>(
    reduxStatus
  );
  const [isLocalPolling, setIsLocalPolling] = useState(!!reduxStatus);

  // Get current filters from state
  const { searchText, departmentFilter, isActiveFilter, page, limit } =
    useSelector((state: RootState) => ({
      searchText: state.labTests.searchText,
      departmentFilter: state.labTests.departmentFilter,
      isActiveFilter: state.labTests.isActiveFilter,
      page: state.labTests.page,
      limit: state.labTests.limit,
    }));

  const pollingRef = useRef<{
    timeout: NodeJS.Timeout | null;
    isMounted: boolean;
    shouldContinue: boolean;
  }>({ timeout: null, isMounted: true, shouldContinue: true });

  // Update local status and polling state when redux status changes
  useEffect(() => {
    if (reduxStatus) {
      setLocalStatus(reduxStatus);
      setIsLocalPolling(true);
    }
  }, [reduxStatus]);

  // Sync local polling state with redux
  useEffect(() => {
    if (isReduxPolling !== isLocalPolling) {
      setIsLocalPolling(isReduxPolling);
    }
  }, [isReduxPolling]);

  // Start/stop polling based on polling state
  useEffect(() => {
    const { current } = pollingRef;

    // Cleanup function
    const cleanup = () => {
      if (current.timeout) {
        clearTimeout(current.timeout);
        current.timeout = null;
      }
      current.isMounted = false;
      current.shouldContinue = false;
    };

    // If we don't have a status or polling is not active, clean up and return
    if (!localStatus || !isLocalPolling) {
      cleanup();
      return;
    }

    // Reset polling state
    current.isMounted = true;
    current.shouldContinue = true;

    // Use the full status URL from the response
    // The statusUrl should be like '/loinc/update/status/c55ffd6a-1686-4c1e-8914-a8faf9f226d6'
    const statusUrl = localStatus.statusUrl;

    if (!statusUrl) {
      console.error('No status URL provided for bulk update status check');
      return;
    }

    const poll = async () => {
      if (!current.isMounted || !current.shouldContinue) return;

      try {
        // Pass the full status URL to the checkBulkUpdateStatus thunk
        const result = await dispatch(checkBulkUpdateStatus(statusUrl));

        if (!current.isMounted) return;

        if (isFulfilled(result) && result.payload) {
          const updatedStatus = result.payload;
          const previousStatus = localStatus?.status;

          // Update the status in both local and redux state
          setLocalStatus(updatedStatus);
          dispatch(updateBulkUpdateStatus(updatedStatus));

          // Continue polling if still processing
          if (
            updatedStatus.status === 'PROCESSING' ||
            updatedStatus.status === 'PENDING'
          ) {
            current.timeout = setTimeout(poll, 5000);
          } else if (updatedStatus.status === 'COMPLETED') {
            // If just completed, fetch the latest lab tests
            if (previousStatus !== 'COMPLETED') {
              if (organizationId) {
                const params: any = {
                  searchText,
                  department:
                    departmentFilter === 'all'
                      ? ''
                      : getDepartmentFilterValue(departmentFilter),
                  page,
                  pageSize: limit,
                  organizationId,
                };

                // Only include isActive if it's not 'all'
                if (isActiveFilter !== 'all') {
                  params.isActive = isActiveFilter === 'active';
                }

                fetchLabTests(params);
              } else {
                console.error('No organizationId available to fetch lab tests');
              }
            }
            // Stop polling but keep showing the status
            current.shouldContinue = false;
          } else if (updatedStatus.status === 'FAILED') {
            // Stop polling on failure
            current.shouldContinue = false;
          }
        }
      } catch (error) {
        console.error('Error in polling:', error);
        // Retry on error after delay
        if (current.isMounted && current.shouldContinue) {
          current.timeout = setTimeout(poll, 5000);
        }
      }
    };

    // Start polling
    poll();

    // Cleanup on unmount or when dependencies change
    return cleanup;
  }, [
    dispatch,
    localStatus?.id,
    isLocalPolling,
    searchText,
    departmentFilter,
    isActiveFilter,
    page,
    limit,
  ]);

  // Handle close button click
  const handleClose = () => {
    const { current } = pollingRef;
    current.shouldContinue = false;
    if (current.timeout) {
      clearTimeout(current.timeout);
      current.timeout = null;
    }
    setIsLocalPolling(false);
    dispatch(stopBulkUpdatePolling());
  };

  // Don't render anything if show is false, there's no status, or if polling is not active
  if (!show || !localStatus || !isLocalPolling) {
    return null;
  }

  return (
    <div className='fixed bottom-4 right-4 z-50'>
      <BulkUpdateStatusComponent
        status={localStatus}
        isPolling={isLocalPolling}
        onClose={handleClose}
      />
    </div>
  );
};
