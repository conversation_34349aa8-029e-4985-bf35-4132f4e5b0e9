import { Calendar, ChevronLeft, ChevronRight, X } from 'lucide-react';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { createPortal } from 'react-dom';

export interface DatePickerProps {
  value: Date | null;
  onChange: (date: Date | null) => void;
  placeholder?: string;
  className?: string;
  showYearDropdown?: boolean;
  showMonthDropdown?: boolean;
  minDate?: Date;
  maxDate?: Date;
  portalContainer?: HTMLElement | null;
}

const DatePicker: React.FC<DatePickerProps> = ({
  value,
  onChange,
  placeholder = 'Select date',
  className = '',
  showYearDropdown = true,
  showMonthDropdown = true,
  minDate,
  maxDate,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [currentMonth, setCurrentMonth] = useState(() => value || new Date());
  const [selectedYear, setSelectedYear] = useState(() =>
    (value || new Date()).getFullYear()
  );
  const [selectedMonth, setSelectedMonth] = useState(() =>
    (value || new Date()).getMonth()
  );
  const [dropdownPosition, setDropdownPosition] = useState<'top' | 'bottom'>(
    'bottom'
  );
  const containerRef = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Generate year options (current year ± 50 years)
  const currentYear = new Date().getFullYear();
  const yearOptions = Array.from(
    { length: 101 },
    (_, i) => currentYear - 50 + i
  );

  const monthNames = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ];

  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  // Calculate dropdown position based on available space
  const updateDropdownPosition = useCallback(() => {
    if (!containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const spaceBelow = window.innerHeight - rect.bottom;
    const spaceAbove = rect.top;
    const dropdownHeight = 400; // Approximate height of the dropdown

    setDropdownPosition(
      spaceBelow > dropdownHeight || spaceBelow > spaceAbove ? 'bottom' : 'top'
    );
  }, []);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node) &&
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  useEffect(() => {
    if (isOpen) {
      updateDropdownPosition();
      window.addEventListener('resize', updateDropdownPosition);
      window.addEventListener('scroll', updateDropdownPosition, true);
    } else {
      window.removeEventListener('resize', updateDropdownPosition);
      window.removeEventListener('scroll', updateDropdownPosition, true);
    }
    return () => {
      window.removeEventListener('resize', updateDropdownPosition);
      window.removeEventListener('scroll', updateDropdownPosition, true);
    };
  }, [isOpen, updateDropdownPosition]);

  const toggleDropdown = () => {
    if (!isOpen) {
      updateDropdownPosition();
    }
    setIsOpen(!isOpen);
  };

  useEffect(() => {
    const newDate = new Date(selectedYear, selectedMonth, 1);
    setCurrentMonth(newDate);
  }, [selectedYear, selectedMonth]);

  const formatDate = (date: Date | null): string => {
    if (!date) return '';
    return date.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  const getDaysInMonth = (date: Date): Date[] => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());

    const days: Date[] = [];
    for (let i = 0; i < 42; i++) {
      const day = new Date(startDate);
      day.setDate(startDate.getDate() + i);
      days.push(day);
    }
    return days;
  };

  const isSameDay = (date1: Date, date2: Date): boolean => {
    return (
      date1.getDate() === date2.getDate() &&
      date1.getMonth() === date2.getMonth() &&
      date1.getFullYear() === date2.getFullYear()
    );
  };

  const isCurrentMonth = (date: Date): boolean => {
    return (
      date.getMonth() === currentMonth.getMonth() &&
      date.getFullYear() === currentMonth.getFullYear()
    );
  };

  const isToday = (date: Date): boolean => {
    return isSameDay(date, new Date());
  };

  const isDateDisabled = (date: Date): boolean => {
    if (minDate && date < minDate) return true;
    if (maxDate && date > maxDate) return true;
    return false;
  };

  const handleDateClick = (date: Date) => {
    if (isDateDisabled(date)) return;
    onChange(date);
    setIsOpen(false);
  };

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    onChange(null);
    setIsOpen(false);
  };

  const handleYearChange = (year: number) => {
    setSelectedYear(year);
  };

  const handleMonthChange = (month: number) => {
    setSelectedMonth(month);
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    if (direction === 'next') {
      if (selectedMonth === 11) {
        setSelectedYear(selectedYear + 1);
        setSelectedMonth(0);
      } else {
        setSelectedMonth(selectedMonth + 1);
      }
    } else {
      if (selectedMonth === 0) {
        setSelectedYear(selectedYear - 1);
        setSelectedMonth(11);
      } else {
        setSelectedMonth(selectedMonth - 1);
      }
    }
  };

  const renderDropdown = () => {
    if (!isOpen) return null;

    const dropdownElement = (
      <div
        ref={dropdownRef}
        className='fixed bg-white border border-gray-200 rounded-lg shadow-lg z-[9999] p-4 min-w-[320px] w-auto'
        style={{
          left: containerRef.current
            ? `${containerRef.current.getBoundingClientRect().left}px`
            : 'auto',
          top:
            dropdownPosition === 'bottom' && containerRef.current
              ? `${containerRef.current.getBoundingClientRect().bottom + window.scrollY + 4}px`
              : 'auto',
          bottom:
            dropdownPosition === 'top' && containerRef.current
              ? `${window.innerHeight - containerRef.current.getBoundingClientRect().top - window.scrollY + 4}px`
              : 'auto',
        }}
      >
        {/* Dropdown content */}
        <div className='flex items-center justify-between mb-4 space-x-2'>
          <button
            type='button'
            onClick={() => navigateMonth('prev')}
            className='p-2 hover:bg-gray-100 rounded-full transition-colors'
          >
            <ChevronLeft className='w-4 h-4' />
          </button>
          <div className='flex items-center space-x-2 flex-1 justify-center'>
            {showMonthDropdown && (
              <select
                value={selectedMonth}
                onChange={(e) => handleMonthChange(parseInt(e.target.value))}
                className='px-2 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent'
              >
                {monthNames.map((month, index) => (
                  <option key={index} value={index}>
                    {month}
                  </option>
                ))}
              </select>
            )}
            {showYearDropdown && (
              <select
                value={selectedYear}
                onChange={(e) => handleYearChange(parseInt(e.target.value))}
                className='px-2 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent'
              >
                {yearOptions.map((year) => (
                  <option key={year} value={year}>
                    {year}
                  </option>
                ))}
              </select>
            )}
          </div>
          <button
            type='button'
            onClick={() => navigateMonth('next')}
            className='p-2 hover:bg-gray-100 rounded-full transition-colors'
          >
            <ChevronRight className='w-4 h-4' />
          </button>
        </div>
        <div className='grid grid-cols-7 gap-1 mb-2'>
          {dayNames.map((day) => (
            <div
              key={day}
              className='text-center text-xs font-medium text-gray-500 py-2'
            >
              {day}
            </div>
          ))}
        </div>
        <div className='grid grid-cols-7 gap-1'>
          {getDaysInMonth(currentMonth).map((date, index) => {
            const isSelected = value && isSameDay(date, value);
            const isCurrentMonthDay = isCurrentMonth(date);
            const isTodayDate = isToday(date);
            const isDisabled = isDateDisabled(date);
            return (
              <button
                key={index}
                type='button'
                onClick={() => handleDateClick(date)}
                disabled={isDisabled}
                className={`p-2 text-sm rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors
                  ${!isCurrentMonthDay ? 'text-gray-300' : 'text-gray-900'}
                  ${isSelected ? 'bg-blue-500 text-white hover:bg-blue-600' : ''}
                  ${!isSelected && isTodayDate ? 'bg-blue-100 text-blue-600 font-medium' : ''}
                  ${!isSelected && !isTodayDate && isCurrentMonthDay ? 'hover:bg-gray-100' : ''}
                  ${isDisabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                `}
              >
                {date.getDate()}
              </button>
            );
          })}
        </div>
      </div>
    );

    // If a portal container is provided, use it, otherwise use document.body
    const target = document.body;
    return createPortal(dropdownElement, target);
  };

  return (
    <div className='relative' ref={containerRef}>
      <div
        className={`flex items-center justify-between cursor-pointer bg-white border border-gray-300 rounded-lg hover:border-gray-400 focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-transparent transition-colors ${className}`}
        onClick={toggleDropdown}
      >
        <span
          className={`flex-1 px-2 py-1 ${value ? 'text-gray-900' : 'text-gray-500'} text-sm`}
        >
          {value ? formatDate(value) : placeholder}
        </span>
        <div className='flex items-center space-x-1 pr-2'>
          {value && (
            <button
              type='button'
              onClick={handleClear}
              className='p-0.5 text-gray-400 hover:text-gray-600 transition-colors'
            >
              <X className='w-3 h-3' />
            </button>
          )}
          <Calendar className='w-3 h-3 text-gray-400' />
        </div>
      </div>
      {renderDropdown()}
    </div>
  );
};

export default DatePicker;
