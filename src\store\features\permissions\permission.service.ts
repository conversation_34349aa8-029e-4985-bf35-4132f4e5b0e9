import { PERMISSIONS_ENDPOINT } from '../../../constants/api-endpoints';
import api from '../../../services/api';

export type AssignPermissionsData = {
  roleId: string;
  permissions: string[]; // Array of permission IDs
};

export type GetRolePermissionsParams = {
  roleId: string;
};

/**
 * Assign permissions to a role
 */
const assignPermissions = async (data: AssignPermissionsData) => {
  const response = await api.post(
    `${PERMISSIONS_ENDPOINT}/assign-permissions`,
    {
      roleId: data.roleId,
      permissions: data.permissions,
    }
  );
  return response.data;
};

/**
 * Update permissions for a role (PATCH method)
 */
const updatePermissions = async (data: AssignPermissionsData) => {
  const response = await api.post(
    `${PERMISSIONS_ENDPOINT}/assign-permissions`,
    {
      roleId: data.roleId,
      permissions: data.permissions,
    }
  );
  return response.data;
};

/**
 * Get permissions assigned to a specific role
 */
const getRolePermissions = async (params: GetRolePermissionsParams) => {
  const response = await api.get(
    `${PERMISSIONS_ENDPOINT}/permissions/api-list`,
    {
      params: {
        roleId: params.roleId,
      },
    }
  );
  return response.data;
};

const permissionService = {
  assignPermissions,
  updatePermissions,
  getRolePermissions,
};

export default permissionService;
