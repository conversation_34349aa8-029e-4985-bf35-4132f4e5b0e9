import { Download, Trash2 } from 'lucide-react';
import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';

import AppButton from '../../../components/Common/AppButton';
import ConfirmationModal from '../../../components/Common/ConfirmationModal';
import DataTable, { Column } from '../../../components/Common/DataTable';
import Modal from '../../../components/Common/Modal';
import SelectInput from '../../../components/Common/MUISelect';
import SearchBar from '../../../components/Common/SearchBar';
import StatusBadge from '../../../components/Common/StatusBadge';
import Tooltip from '../../../components/Common/Tooltip';
import { getCurrentOrganizationId } from '../../../utils/auth-utils';
import { useLabTests } from '../hooks/useLabTests';
import {
  fetchLabTestDepartments,
  getDepartmentFilterValue,
  LabTestDepartment,
} from '../services/department.service';
import {
  LabTestListItem,
  LabTestListParams,
  UpdateLabTestRequest,
} from '../types/labTest.types';
import { BulkUpdateStatusIndicator } from './BulkUpdateStatusIndicator';
import LabTestForm from './LabTestForm';

interface LabTestListProps {
  organizationName: string;
  organizationId?: string;
}

const LabTestList: React.FC<LabTestListProps> = memo(
  ({ organizationName, organizationId }) => {
    const currentOrgId = organizationId || getCurrentOrganizationId();

    const [showEditModal, setShowEditModal] = useState(false);
    const [editingTest, setEditingTest] = useState<LabTestListItem | null>(
      null
    );
    const [departments, setDepartments] = useState<LabTestDepartment[]>([]);
    const [loadingDepartments, setLoadingDepartments] = useState(true);
    const [showBulkUpdateStatus, setShowBulkUpdateStatus] = useState(false);
    const [showConfirmModal, setShowConfirmModal] = useState(false);
    const [isRemoving, setIsRemoving] = useState(false);
    const [isUpdating, setIsUpdating] = useState(false);

    const {
      tests,
      loading,
      error: _error,
      updating,
      searchText,
      page,
      limit,
      total,
      departmentFilter,
      isActiveFilter,
      selectedTests,
      isAllSelected,
      continuationToken,
      hasMoreResults,
      totalPages: _totalPages,
      fetchLabTests: _fetchLabTests,
      updateLabTests,
      removeLabTests,
      exportLabTests,
      search,
      filterByDepartment,
      selectTest,
      selectAllTests,
      clearSelection: _clearSelection,
      clearMessages: _clearMessages,
      updateTestInList: _updateTestInList,
      hasSelection,
      selectedCount,
      bulkUpdate,
    } = useLabTests(currentOrgId || undefined);

    // Clear selection only when component unmounts
    useEffect(() => {
      return () => {
        _clearSelection();
      };
    }, [_clearSelection]);

    // Check if bulk update is in progress
    const isBulkUpdateInProgress = updating || bulkUpdate.isPolling;

    // Load departments on mount
    React.useEffect(() => {
      const loadDepartments = async () => {
        try {
          setLoadingDepartments(true);
          const depts = await fetchLabTestDepartments();
          setDepartments(depts);
        } catch {
          // Failed to load departments, will use fallback
        } finally {
          setLoadingDepartments(false);
        }
      };

      loadDepartments();
    }, []);

    // Handle inline toggle for isActive - removed since status is now non-clickable

    // Handle save test (call API to update)
    const handleSaveTest = useCallback(
      async (
        testId: string,
        data: {
          organizationCost: number;
          organizationPrice?: number;
          department?: string;
          departments?: string[];
          isActive: boolean;
        }
      ): Promise<void> => {
        if (!currentOrgId) return;

        try {
          // Use organizationPrice if available, otherwise fallback to organizationCost
          const price = data.organizationPrice ?? data.organizationCost;

          // Prepare API data
          const updateData = {
            organizationId: currentOrgId,
            tests: [
              {
                testId,
                isActive: data.isActive,
                price,
                departments:
                  data.departments ||
                  (data.department ? [data.department] : []),
              },
            ],
            suppressStatusIndicator: true, // Don't show status for single updates
          };

          // Call the update API
          await updateLabTests(updateData);

          // Prepare params object with required fields
          const params: Record<string, any> = {
            organizationId: currentOrgId,
            searchText,
            page,
            pageSize: limit,
            isActive:
              isActiveFilter === 'all'
                ? undefined
                : isActiveFilter === 'active',
          };

          // Only include department if not 'all'
          if (departmentFilter !== 'all') {
            params.department = departmentFilter;
          }

          // Type assertion to Partial<LabTestListParams> since we've ensured types are correct
          await _fetchLabTests(params as Partial<LabTestListParams>);
        } catch (error) {
          console.error('Error saving test:', error);
          throw error; // Re-throw to let the form handle the error
        }
      },
      [
        currentOrgId,
        updateLabTests,
        _fetchLabTests,
        searchText,
        departmentFilter,
        isActiveFilter,
        page,
        limit,
      ]
    );

    // Handle bulk operations - directly call API
    const handleBulkUpdate = useCallback(async () => {
      if (!currentOrgId) return;

      setIsUpdating(true);

      // For select all, we'll let the BulkUpdateStatusIndicator handle the polling
      // For individual selections, we'll refresh the list immediately after update
      const isSelectAllOperation = isAllSelected;

      try {
        const updateData: UpdateLabTestRequest = {
          organizationId: currentOrgId,
          tests: isSelectAllOperation
            ? []
            : selectedTests.map((testId) => ({
                testId,
                isActive: true,
                price: 0,
                departments: [],
              })),
          selectAll: isSelectAllOperation,
          department: isSelectAllOperation
            ? getDepartmentFilterValue(departmentFilter)
            : '',
        };

        // Show status indicator for select all operations
        if (isSelectAllOperation) {
          setShowBulkUpdateStatus(true);
        }

        // Perform the update
        await updateLabTests({
          ...updateData,
          // Only suppress status for individual selections
          suppressStatusIndicator: !isSelectAllOperation,
        });

        // For individual selections, refresh the list immediately
        if (!isSelectAllOperation) {
          const params: Record<string, any> = {
            organizationId: currentOrgId,
            searchText,
            page,
            pageSize: limit,
            isActive:
              isActiveFilter === 'all'
                ? undefined
                : isActiveFilter === 'active',
          };

          if (departmentFilter !== 'all') {
            params.department = getDepartmentFilterValue(departmentFilter);
          }

          await _fetchLabTests(params as Partial<LabTestListParams>);
        }
      } catch (error) {
        console.error('Error updating lab tests:', error);
        // Error handling is done in the Redux slice
      } finally {
        setIsUpdating(false);
      }
    }, [
      currentOrgId,
      departmentFilter,
      isActiveFilter,
      isAllSelected,
      selectedTests,
      updateLabTests,
      _clearSelection,
      _fetchLabTests,
      searchText,
      page,
      limit,
    ]);

    // Handle remove operation
    const handleRemove = useCallback(async () => {
      if (!currentOrgId || (!selectedTests.length && !isAllSelected)) {
        return;
      }

      setIsRemoving(true);

      if (isAllSelected) {
        setShowBulkUpdateStatus(true);
      }

      try {
        // Call the remove API (no parameters needed, it uses the current selection)
        const result = await removeLabTests();

        // If the removal was successful, refresh the list
        if (
          result &&
          typeof result === 'object' &&
          'success' in result &&
          result.success
        ) {
          const params: Record<string, any> = {
            organizationId: currentOrgId,
            searchText,
            page,
            pageSize: limit,
            isActive:
              isActiveFilter === 'all'
                ? undefined
                : isActiveFilter === 'active',
          };

          if (departmentFilter !== 'all') {
            params.department = getDepartmentFilterValue(departmentFilter);
          }

          await _fetchLabTests(params as Partial<LabTestListParams>);
        }

        // Close the confirmation modal
        setShowConfirmModal(false);
      } catch (error) {
        console.error('Error removing lab tests:', error);
      } finally {
        setIsRemoving(false);
      }
    }, [
      currentOrgId,
      selectedTests,
      isAllSelected,
      departmentFilter,
      isActiveFilter,
      removeLabTests,
      _fetchLabTests,
      searchText,
      page,
      limit,
    ]);

    const confirmRemove = useCallback(() => {
      setShowConfirmModal(true);
    }, [setShowConfirmModal]);

    const cancelRemove = useCallback(() => {
      setShowConfirmModal(false);
    }, [setShowConfirmModal]);

    // Handle mark all tests (across all pages)
    const handleMarkAll = useCallback(() => {
      selectAllTests();
    }, [selectAllTests]);

    // Handle export
    const handleExport = useCallback(() => {
      exportLabTests();
    }, [exportLabTests]);

    // Handle search
    const handleSearchChange = useCallback(
      (searchTerm: string) => {
        search(searchTerm);
      },
      [search]
    );

    // Handle department filter
    const handleDepartmentFilterChange = useCallback(
      (department: string) => {
        // Store the original selected value in Redux, not the transformed value
        filterByDepartment(department);
      },
      [filterByDepartment]
    );

    // Handle status filter
    // const handleStatusFilterChange = useCallback(
    //   (status: string) => {
    //     filterByStatus(status);
    //   },
    //   [filterByStatus]
    // );

    // Pagination handler for new API
    const handlePageChange = useCallback(
      (newPage: number) => {
        if (newPage > page && hasMoreResults && continuationToken) {
          _fetchLabTests({ continuationToken });
        } else {
          _fetchLabTests({ page: newPage });
        }
      },
      [_fetchLabTests, hasMoreResults, continuationToken, page]
    );

    // Calculate counts of active and inactive selected items
    const { activeCount, inactiveCount } = useMemo(() => {
      if (isAllSelected) {
        // If all are selected, we don't know the exact counts without fetching all
        return { activeCount: selectedCount, inactiveCount: selectedCount };
      }

      // Calculate counts based on current selection
      const active = tests.filter(
        (test) => selectedTests.includes(test.testId) && test.isActive
      ).length;
      const inactive = tests.filter(
        (test) => selectedTests.includes(test.testId) && !test.isActive
      ).length;

      return { activeCount: active, inactiveCount: inactive };
    }, [selectedTests, tests, isAllSelected, selectedCount]);

    // Handle selection of a single test
    const handleSelectTest = useCallback(
      (testId: string, selected: boolean) => {
        // The selectTest action will handle adding/removing the test ID
        // based on the selected parameter
        if (selected) {
          selectTest(testId);
        } else {
          // If unchecking, we need to remove the test ID from the selection
          selectTest(testId); // The Redux action will handle toggling
        }
      },
      [selectTest]
    );

    // Removed auto-selection of active tests to allow manual selection

    // Memoized columns definition
    const columns: Column<LabTestListItem>[] = useMemo(
      () => [
        {
          key: 'testName',
          label: 'Test Name',
          render: (_, test) => (
            <div style={{ maxWidth: 200 }}>
              <div className='text-sm font-medium text-gray-900'>
                {test.displayName || test.testName}
              </div>
              <Tooltip content={test.shortName || test.testName}>
                <div
                  className='text-xs text-gray-500 cursor-help'
                  style={{
                    maxWidth: 250,
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    display: 'block',
                  }}
                >
                  {test.shortName || test.testName}
                </div>
              </Tooltip>
            </div>
          ),
          width: '300px',
        },
        {
          key: 'defaultCost',
          label: 'Cost',
          render: (_, test) => (
            <span className='text-sm font-medium text-gray-900'>
              ₹{(test.defaultCost || 0).toFixed(2)}
            </span>
          ),
          width: '180px',
        },
        {
          key: 'organizationPrice',
          label: 'Organization Price',
          render: (_, test) => (
            <span className='text-sm font-medium text-blue-600'>
              {test.organizationPrice
                ? `₹${test.organizationPrice.toFixed(2)}`
                : '-'}
            </span>
          ),
        },
        {
          key: 'departments',
          label: 'Department',
          render: (_, test) => (
            <div className='flex flex-wrap gap-1'>
              {test.class ? (
                <span className='inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800'>
                  {test.class}
                </span>
              ) : (
                <span className='text-sm text-gray-500'>None</span>
              )}
            </div>
          ),
        },
        {
          key: 'isActive',
          label: 'Status',
          render: (_, test) => (
            <StatusBadge
              status={test.isActive ? 'active' : 'inactive'}
              size='sm'
            />
          ),
        },
      ],
      [
        selectedTests,
        handleSelectTest,
        handleMarkAll,
        isAllSelected,
        tests.length,
      ]
    );

    return (
      <div className='space-y-6'>
        <BulkUpdateStatusIndicator show={showBulkUpdateStatus} />

        {/* Confirmation Modal */}
        <ConfirmationModal
          isOpen={showConfirmModal}
          onClose={cancelRemove}
          onConfirm={handleRemove}
          title='Confirm Removal'
          message={
            isAllSelected
              ? 'Are you sure you want to remove all selected lab tests? This action cannot be undone.'
              : `Are you sure you want to remove ${selectedCount} selected lab test(s)?`
          }
          confirmText='Remove'
          cancelText='Cancel'
          type='danger'
          loading={isRemoving}
        />

        <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4'>
          <div>
            <h1 className='text-2xl font-bold text-gray-900'>Lab Tests</h1>
            <p className='mt-1 text-sm text-gray-500'>
              Manage lab tests for {organizationName}
            </p>
          </div>
          <div className='flex items-center space-x-3'>
            <SearchBar
              value={searchText}
              onChange={handleSearchChange}
              placeholder='Search lab tests...'
              size='md'
            />
            {hasSelection && (
              <>
                <AppButton
                  onClick={handleBulkUpdate}
                  disabled={
                    !hasSelection ||
                    loading ||
                    isBulkUpdateInProgress ||
                    inactiveCount === 0 ||
                    isUpdating
                  }
                  kind='primary'
                  startIcon={
                    isUpdating ? (
                      <div className='w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin' />
                    ) : null
                  }
                >
                  {isUpdating
                    ? `Updating... ${inactiveCount > 0 ? `(${inactiveCount})` : ''}`
                    : `Bulk Update${inactiveCount > 0 ? ` (${inactiveCount})` : ''}`}
                </AppButton>
                <AppButton
                  onClick={confirmRemove}
                  disabled={
                    !hasSelection ||
                    loading ||
                    isBulkUpdateInProgress ||
                    activeCount === 0 ||
                    isRemoving
                  }
                  kind='primary'
                  startIcon={
                    isRemoving ? (
                      <div className='w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin' />
                    ) : (
                      <Trash2 className='w-4 h-4' />
                    )
                  }
                  style={{
                    backgroundColor:
                      !hasSelection ||
                      loading ||
                      isBulkUpdateInProgress ||
                      activeCount === 0 ||
                      isRemoving
                        ? 'rgba(0, 0, 0, 0.12)'
                        : '#dc2626',
                  }}
                >
                  Remove{activeCount > 0 ? ` (${activeCount})` : ''}
                </AppButton>
              </>
            )}

            {/* Temporarily hidden Import Excel button */}
            {/* eslint-disable-next-line no-constant-binary-expression */}
            {false && (
              <AppButton
                onClick={handleExport}
                disabled={loading}
                kind='secondary'
                startIcon={<Download className='w-4 h-4' />}
                size='small'
              >
                Import Excel
              </AppButton>
            )}
          </div>
        </div>

        {/* Data Table */}
        <DataTable<LabTestListItem>
          columns={columns}
          data={tests}
          loading={loading}
          selectedIds={selectedTests}
          isAllSelected={isAllSelected}
          onSelectAll={selectAllTests}
          onSelectOne={handleSelectTest}
          searchFilters={
            <div className='flex items-center space-x-3'>
              <SelectInput
                value={loadingDepartments ? '' : departmentFilter}
                onChange={(e) => handleDepartmentFilterChange(e.target.value)}
                disabled={loadingDepartments}
                options={
                  loadingDepartments
                    ? [{ label: 'Loading departments...', value: '' }]
                    : departments.map((dept) => ({
                        label: dept.label,
                        value: dept.value,
                      }))
                }
                size='small'
                sx={{ minWidth: 150 }}
              />
              {/* <SelectInput
                value={isActiveFilter}
                onChange={(e) => handleStatusFilterChange(e.target.value)}
                options={statusFilterOptions}
                size='small'
                sx={{ minWidth: 120 }}
              /> */}
            </div>
          }
          pagination={{
            total,
            page,
            limit,
            onPageChange: handlePageChange,
          }}
          onEdit={(test) => {
            setEditingTest(test);
            setShowEditModal(true);
          }}
        />

        {/* Edit Modal */}
        <Modal
          isOpen={showEditModal}
          onClose={() => !updating && setShowEditModal(false)}
          title='Edit Lab Test'
          size='md'
        >
          <div className='px-4 pb-4'>
            <LabTestForm
              test={editingTest}
              onSubmit={handleSaveTest}
              onSuccess={() => {
                setShowEditModal(false);
                setEditingTest(null);
              }}
              onClose={() => !updating && setShowEditModal(false)}
              updating={updating}
            />
          </div>
        </Modal>
      </div>
    );
  }
);

LabTestList.displayName = 'LabTestList';

export default LabTestList;
