import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';

import { BulkUpdateStatus } from '../../../features/lab-tests/types/bulk-update.types';
import {
  LabTestListItem,
  LabTestListParams,
  UpdateLabTestRequest,
} from '../../../features/lab-tests/types/labTest.types';
import { labTestService } from './labTest.service';

// Add this interface for the remove request
interface RemoveLabTestsRequest {
  organizationId: string;
  tests?: string[];
  selectAll?: boolean;
  department?: string;
}

// State interface
interface BulkUpdateState {
  status: BulkUpdateStatus | null;
  isPolling: boolean;
  error: string | null;
}

interface LabTestState {
  tests: LabTestListItem[];
  loading: boolean;
  updating: boolean;
  error: string | null;
  successMessage: string | null;
  total: number;
  page: number;
  limit: number;
  searchText: string;
  departmentFilter: string;
  isActiveFilter: string;
  selectedTests: string[];
  isAllSelected: boolean; // Track if "mark all" across pages is selected
  continuationToken: string;
  hasMoreResults: boolean;
  totalFetched: number;
  totalPages: number;
  bulkUpdate: BulkUpdateState;
  exporting: boolean;
  exportError: string | null;
}

// Initial state
const initialState: LabTestState = {
  tests: [],
  loading: false,
  updating: false,
  error: null,
  successMessage: null,
  total: 0,
  page: 1,
  limit: 100, // Increased page size to 100
  searchText: '',
  departmentFilter: 'all',
  isActiveFilter: 'all',
  selectedTests: [],
  isAllSelected: false,
  continuationToken: '',
  hasMoreResults: false,
  totalFetched: 0,
  totalPages: 1,
  bulkUpdate: {
    status: null,
    isPolling: false,
    error: null,
  },
  exporting: false,
  exportError: null,
};

// Async thunks
export const fetchLabTests = createAsyncThunk(
  'labTests/fetchLabTests',
  async (params: LabTestListParams) => {
    const response = await labTestService.fetchLabTestsList(params);
    return response;
  }
);

interface StandardUpdateResponse {
  success: boolean;
  message: string;
}

interface BulkUpdateStartResponse {
  async: boolean;
  jobId: string;
  message: string;
  statusUrl: string;
}

type UpdateLabTestsResponse = StandardUpdateResponse | BulkUpdateStartResponse;

export const updateLabTests = createAsyncThunk<
  UpdateLabTestsResponse,
  UpdateLabTestRequest,
  { rejectValue: string }
>(
  'labTests/updateLabTests',
  async (updateData, { rejectWithValue, dispatch }) => {
    try {
      const isBulk =
        updateData.selectAll === true || (updateData.tests?.length ?? 0) === 0;

      if (isBulk) {
        dispatch(startBulkUpdatePolling());

        dispatch(
          updateBulkUpdateStatus({
            id: `temp-${Date.now()}`,
            status: 'PROCESSING',
            progress: 0,
            totalItems: 0,
            processedItems: 0,
            startTime: new Date().toISOString(),
            endTime: null,
            error: null,
            message: 'Starting bulk update...',
            result: null,
            type: 'LOINC_UPDATE',
            statusUrl: '',
          })
        );
      }

      const response = await labTestService.updateLabTests(updateData);

      if ('async' in response && response.async) {
        dispatch(
          updateBulkUpdateStatus({
            id: response.jobId,
            status: 'PROCESSING',
            progress: 0,
            totalItems: 0,
            processedItems: 0,
            startTime: new Date().toISOString(),
            endTime: null,
            error: null,
            message: response.message || 'Processing bulk update...',
            result: null,
            type: 'LOINC_UPDATE',
            statusUrl: response.statusUrl,
          })
        );
      } else if ('success' in response) {
        dispatch(stopBulkUpdatePolling());
      }
      return response;
    } catch (error: any) {
      dispatch(stopBulkUpdatePolling());
      dispatch(
        setBulkUpdateError(
          error.response?.data?.message || 'Failed to update lab tests'
        )
      );
      return rejectWithValue(
        error.response?.data?.message || 'Failed to update lab tests'
      );
    }
  }
);

export const removeLabTests = createAsyncThunk<
  StandardUpdateResponse | BulkUpdateStartResponse,
  RemoveLabTestsRequest,
  { rejectValue: string }
>('labTests/removeLabTests', async (data, { rejectWithValue, dispatch }) => {
  try {
    const isBulk = data.selectAll === true || (data.tests?.length ?? 0) === 0;

    if (isBulk) {
      dispatch(startBulkUpdatePolling());

      dispatch(
        updateBulkUpdateStatus({
          id: `temp-${Date.now()}`,
          status: 'PROCESSING',
          progress: 0,
          totalItems: 0,
          processedItems: 0,
          startTime: new Date().toISOString(),
          endTime: null,
          error: null,
          message: 'Starting bulk removal...',
          result: null,
          type: 'LOINC_REMOVE',
          statusUrl: '',
        })
      );
    }

    const response = await labTestService.removeLabTests(data);

    if ('async' in response && response.async) {
      dispatch(
        updateBulkUpdateStatus({
          id: response.jobId,
          status: 'PROCESSING',
          progress: 0,
          totalItems: 0,
          processedItems: 0,
          startTime: new Date().toISOString(),
          endTime: null,
          error: null,
          message: response.message || 'Processing bulk removal...',
          result: null,
          type: 'LOINC_REMOVE',
          statusUrl: response.statusUrl,
        })
      );
    } else if ('success' in response) {
      dispatch(stopBulkUpdatePolling());
    }

    return response;
  } catch (error: any) {
    dispatch(stopBulkUpdatePolling());
    dispatch(
      setBulkUpdateError(
        error.response?.data?.message || 'Failed to remove lab tests'
      )
    );
    return rejectWithValue(
      error.response?.data?.message || 'Failed to remove lab tests'
    );
  }
});

export const checkBulkUpdateStatus = createAsyncThunk<
  BulkUpdateStatus,
  string,
  {
    rejectValue: { message: string; response?: any };
    state: { labTests: LabTestState };
  }
>('labTests/checkBulkUpdateStatus', async (statusUrl, { rejectWithValue }) => {
  try {
    const response = await labTestService.checkBulkUpdateStatus(statusUrl);
    return response;
  } catch (error: any) {
    if (error.name === 'AbortError') {
      return rejectWithValue({ message: 'Polling was aborted' });
    }

    console.error('Error checking bulk update status:', error);

    // Handle different error response formats
    if (error.response) {
      const errorMessage =
        typeof error.response.data === 'string'
          ? error.response.data
          : error.response.data?.message || 'Failed to check update status';

      return rejectWithValue({
        message: errorMessage,
        response: error.response.data,
      });
    }

    return rejectWithValue({
      message: error.message || 'Failed to check update status',
    });
  }
});

export const exportLabTests = createAsyncThunk(
  'labTests/exportLabTests',
  async (params: LabTestListParams, { rejectWithValue }) => {
    try {
      const blob = await labTestService.exportLabTests(params);
      const filename = `lab-tests-list-${new Date().toISOString().split('T')[0]}.xlsx`;
      labTestService.downloadExportedFile(blob, filename);
      return { success: true, message: 'Export completed successfully' };
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to export lab tests');
    }
  }
);

// Lab Test slice
const labTestSlice = createSlice({
  name: 'labTests',
  initialState,
  reducers: {
    // Bulk update actions
    startBulkUpdatePolling: (state) => {
      state.bulkUpdate.isPolling = true;
      state.bulkUpdate.error = null;
    },

    updateBulkUpdateStatus: (
      state,
      action: PayloadAction<BulkUpdateStatus>
    ) => {
      state.bulkUpdate.status = action.payload;
    },

    stopBulkUpdatePolling: (state) => {
      state.bulkUpdate.isPolling = false;
    },

    clearBulkUpdateStatus: (state) => {
      state.bulkUpdate = {
        status: null,
        isPolling: false,
        error: null,
      };
    },

    setBulkUpdateError: (state, action: PayloadAction<string>) => {
      state.bulkUpdate.error = action.payload;
      state.bulkUpdate.isPolling = false;
    },

    setSuccessMessage: (state, action: PayloadAction<string>) => {
      state.successMessage = action.payload;
    },

    // Search and filtering
    setSearchText: (state, action: PayloadAction<string>) => {
      state.searchText = action.payload;
      state.page = 1; // Reset to first page when searching
    },

    setDepartmentFilter: (state, action: PayloadAction<string>) => {
      state.departmentFilter = action.payload;
      state.page = 1; // Reset to first page when filtering
    },

    setIsActiveFilter: (state, action: PayloadAction<string>) => {
      state.isActiveFilter = action.payload;
      state.page = 1; // Reset to first page when filtering
    },

    // Pagination
    setPage: (state, action: PayloadAction<number>) => {
      state.page = action.payload;
    },

    setLimit: (state, action: PayloadAction<number>) => {
      state.limit = action.payload;
      state.page = 1; // Reset to first page when changing limit
    },

    // Selection management
    selectTest: (
      state,
      action: PayloadAction<{ testId: string; selected?: boolean } | string>
    ) => {
      let testId: string;
      let selected: boolean | undefined;

      if (typeof action.payload === 'string') {
        // Backward compatibility with existing code
        testId = action.payload;
        selected = !state.selectedTests.includes(testId);
      } else {
        testId = action.payload.testId;
        selected = action.payload.selected;
      }

      if (
        selected === false ||
        (selected === undefined && state.selectedTests.includes(testId))
      ) {
        state.selectedTests = state.selectedTests.filter((id) => id !== testId);
      } else if (selected === true) {
        if (!state.selectedTests.includes(testId)) {
          state.selectedTests.push(testId);
        }
      }

      // If manually unchecking a test, unset isAllSelected
      if (selected === false && state.isAllSelected) {
        state.isAllSelected = false;
      }
    },

    selectAllTests: (state) => {
      // Toggle "mark all" across pages
      if (state.isAllSelected) {
        // If all are selected across pages, deselect all
        state.selectedTests = [];
        state.isAllSelected = false;
      } else {
        // Mark all across pages
        state.selectedTests = state.tests.map((test) => test.testId);
        state.isAllSelected = true;
      }
    },

    clearSelection: (state) => {
      state.selectedTests = [];
      state.isAllSelected = false;
    },

    // Update individual test in the list
    updateTestInList: (
      state,
      action: PayloadAction<{
        testId: string;
        updates: Partial<LabTestListItem>;
      }>
    ) => {
      const { testId, updates } = action.payload;
      const index = state.tests.findIndex((test) => test.testId === testId);
      if (index !== -1) {
        const test = state.tests[index];
        if (test) {
          Object.assign(test, updates);
        }
      }
    },

    // Clear messages
    clearError: (state) => {
      state.error = null;
    },

    clearSuccessMessage: (state) => {
      state.successMessage = null;
    },

    clearMessages: (state) => {
      state.error = null;
      state.successMessage = null;
    },

    // Reset state
    resetLabTestState: () => initialState,
  },
  extraReducers: (builder) => {
    // Fetch lab tests
    builder
      .addCase(fetchLabTests.pending, (state) => {
        state.loading = true;
        state.error = null; // Clear any previous errors
        state.successMessage = null; // Clear any previous success messages
      })
      .addCase(fetchLabTests.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null; // Clear any previous errors
        state.tests = action.payload.tests;
        state.total = action.payload.totalRecords;
        state.page = action.payload.currentPage;
        state.continuationToken = action.payload.continuationToken || '';
        state.hasMoreResults = action.payload.hasMoreResults || false;
        state.totalFetched = action.payload.totalFetched || 0;
        state.totalPages = action.payload.totalPages || 1;

        // Update selection state
        if (state.isAllSelected) {
          state.selectedTests = action.payload.tests.map(
            (test: LabTestListItem) => test.testId
          );
        } else {
          state.selectedTests = [];
        }
      })
      .addCase(fetchLabTests.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch  lab tests';
      });

    // Update lab tests
    builder
      .addCase(updateLabTests.pending, (state) => {
        state.updating = true;
        state.error = null; // Clear any previous errors
        state.successMessage = null; // Clear any previous success messages
      })
      .addCase(updateLabTests.fulfilled, (state, { payload }) => {
        state.updating = false;
        state.error = null; // Clear any previous errors

        // Handle standard response
        if (!('async' in payload) || !payload.async) {
          state.successMessage =
            'success' in payload
              ? payload.message
              : 'Lab tests updated successfully';
          state.selectedTests = [];
        }
        // For async operations, the BulkUpdateStatusIndicator will handle the status
      })
      .addCase(updateLabTests.rejected, (state, { payload }) => {
        state.updating = false;
        // Only set error if there isn't one already to prevent duplicate toasts
        if (!state.error) {
          state.error = (payload as string) || 'Failed to update lab tests';
        }
      });

    // Remove lab tests
    builder
      .addCase(removeLabTests.pending, (state) => {
        state.updating = true;
        state.error = null; // Clear any previous errors
        state.successMessage = null; // Clear any previous success messages
      })
      .addCase(removeLabTests.fulfilled, (state, { payload }) => {
        state.updating = false;
        state.error = null; // Clear any previous errors

        // Handle standard response
        if (!('async' in payload) || !payload.async) {
          state.successMessage =
            'success' in payload
              ? payload.message
              : 'Lab tests removed successfully';
          state.selectedTests = [];
          state.isAllSelected = false;
        }
      })
      .addCase(removeLabTests.rejected, (state, { payload }) => {
        state.updating = false;
        // Only set error if there isn't one already to prevent duplicate toasts
        if (!state.error) {
          state.error = (payload as string) || 'Failed to remove lab tests';
        }
      });

    // Export lab tests
    builder
      .addCase(exportLabTests.pending, (state) => {
        state.exporting = true;
        state.exportError = null;
        state.error = null; // Clear any previous errors
      })
      .addCase(exportLabTests.fulfilled, (state) => {
        state.exporting = false;
        state.exportError = null;
        state.error = null; // Clear any previous errors
      })
      .addCase(exportLabTests.rejected, (state, { payload }) => {
        state.exporting = false;
        // Only set error if there isn't one already to prevent duplicate toasts
        if (!state.error) {
          state.error = (payload as string) || 'Failed to export lab tests';
        }
      });

    // Check bulk update status
    builder
      .addCase(checkBulkUpdateStatus.pending, (state) => {
        state.bulkUpdate.error = null;
        state.error = null; // Clear any previous errors
      })
      .addCase(checkBulkUpdateStatus.fulfilled, (state, { payload }) => {
        // Update the status from the API
        state.bulkUpdate.status = payload;
        state.error = null; // Clear any previous errors

        // If the update is completed or failed, stop polling
        if (payload.status === 'COMPLETED' || payload.status === 'FAILED') {
          state.bulkUpdate.isPolling = false;

          // If the update is completed successfully, clear the selection and show success message
          if (payload.status === 'COMPLETED') {
            state.selectedTests = [];
            state.isAllSelected = false;
            // Only set success message if there isn't already one
            if (!state.successMessage) {
              state.successMessage =
                payload.message || 'Bulk update completed successfully';
            }
          }
        }
      })
      .addCase(checkBulkUpdateStatus.rejected, (state, { payload }) => {
        const errorMessage =
          (payload as { message: string })?.message ||
          'Failed to check update status';

        // Only set error if there isn't one already to prevent duplicate toasts
        if (!state.bulkUpdate.error) {
          state.bulkUpdate.error = errorMessage;
          state.error = errorMessage; // Also set the main error state for toast notifications
        }

        state.bulkUpdate.isPolling = false;
        state.successMessage = null; // Clear any success message on error
      });
  },
});

// Export actions
export const {
  setSearchText,
  setDepartmentFilter,
  setIsActiveFilter,
  setPage,
  setLimit,
  selectTest,
  selectAllTests,
  clearSelection,
  updateTestInList,
  clearError,
  clearSuccessMessage,
  clearMessages,
  resetLabTestState,
  startBulkUpdatePolling,
  stopBulkUpdatePolling,
  updateBulkUpdateStatus,
  clearBulkUpdateStatus,
  setBulkUpdateError,
  setSuccessMessage,
} = labTestSlice.actions;

// Export reducer
export default labTestSlice.reducer;
