import React, { useState } from 'react';
import { Outlet } from 'react-router-dom';

import { useNavigationApiTrigger } from '../../hooks/useNavigationApiTrigger';
import Header from './Header';
import Sidebar from './Sidebar';

const MainLayout: React.FC = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // Navigation-based API triggering - calls APIs when user navigates to specific pages
  useNavigationApiTrigger();

  return (
    <div className='h-screen bg-gray-50 flex overflow-hidden'>
      <Sidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />

      <div className='flex-1 flex flex-col min-w-0'>
        <Header onMenuToggle={() => setSidebarOpen(!sidebarOpen)} />

        <main className='flex-1 p-6 overflow-y-auto'>
          <Outlet />
        </main>
      </div>
    </div>
  );
};

export default MainLayout;
