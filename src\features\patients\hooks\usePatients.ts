import { useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { AppDispatch, RootState } from '../../../store';
import {
  clearFilters,
  clearMessages,
  fetchPatients,
  setAgeRange,
  setDateRange,
  setGenderFilter,
  setPage,
  setSearchText,
  setSorting,
} from '../../../store/features/patients/patient.slice';
import { getCurrentOrganizationId } from '../../../utils/auth-utils';

export const usePatients = (organizationId?: string) => {
  const dispatch = useDispatch<AppDispatch>();
  const {
    patients,
    loading,
    total,
    totalPages,
    page,
    limit,
    searchText,
    genderFilter,
    fromAge,
    toAge,
    fromDate,
    toDate,
    sortBy,
    sortOrder,
    error,
    errorMessage,
    successMessage,
  } = useSelector((state: RootState) => state.patients);

  // Get organization ID from props or localStorage
  const currentOrganizationId = organizationId || getCurrentOrganizationId();

  // Fetch patients function
  const fetchPatientsData = useCallback(() => {
    if (!currentOrganizationId) return;

    const today = new Date();
    const formattedToday = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;

    // Handle date range logic
    let finalFromDate = fromDate;
    let finalToDate = toDate;

    // If only fromDate is provided, set toDate to today
    if (fromDate && !toDate) {
      finalToDate = formattedToday;
    }
    // If only toDate is provided, don't include date range in the request
    else if (!fromDate && toDate) {
      finalFromDate = '';
      finalToDate = '';
    }

    const params: any = {
      organizationId: currentOrganizationId,
      searchText: searchText || undefined,
      gender: genderFilter || undefined,
      fromAge: fromAge || undefined,
      toAge: toAge || undefined,
      sortBy,
      sortOrder,
      pageSize: limit,
      page,
    };

    // Only add date parameters if they have values
    if (finalFromDate) params.fromDate = finalFromDate;
    if (finalToDate) params.toDate = finalToDate;

    // Remove undefined values
    Object.keys(params).forEach((key) => {
      if (params[key] === undefined) {
        delete params[key];
      }
    });

    // Only make the API call if we have both fromDate and toDate or neither
    if (
      (fromDate && toDate) ||
      (!fromDate && !toDate) ||
      (fromDate && !toDate)
    ) {
      dispatch(fetchPatients(params));
    }
  }, [
    dispatch,
    currentOrganizationId,
    searchText,
    genderFilter,
    fromAge,
    toAge,
    fromDate,
    toDate,
    sortBy,
    sortOrder,
    limit,
    page,
  ]);

  // Search function
  const search = useCallback(
    (value: string) => {
      dispatch(setSearchText(value));
    },
    [dispatch]
  );

  // Filter by gender
  const filterByGender = useCallback(
    (gender: string) => {
      dispatch(setGenderFilter(gender));
    },
    [dispatch]
  );

  // Filter by age range
  const filterByAgeRange = useCallback(
    (fromAge: number | null, toAge: number | null) => {
      dispatch(setAgeRange({ fromAge, toAge }));
    },
    [dispatch]
  );

  // Filter by date range
  const filterByDateRange = useCallback(
    (fromDate: string, toDate: string) => {
      dispatch(setDateRange({ fromDate, toDate }));
    },
    [dispatch]
  );

  // Change page
  const changePage = useCallback(
    (newPage: number) => {
      dispatch(setPage(newPage));
    },
    [dispatch]
  );

  // Change sorting
  const changeSorting = useCallback(
    (sortBy: string, sortOrder: 'asc' | 'desc') => {
      dispatch(setSorting({ sortBy, sortOrder }));
    },
    [dispatch]
  );

  // Clear all filters
  const clearAllFilters = useCallback(() => {
    dispatch(clearFilters());
  }, [dispatch]);

  // Clear messages
  const clearAllMessages = useCallback(() => {
    dispatch(clearMessages());
  }, [dispatch]);

  // Auto-fetch when dependencies change
  useEffect(() => {
    fetchPatientsData();
  }, [fetchPatientsData]);

  return {
    // Data
    patients,
    loading,
    total,
    totalPages,
    page,
    limit,
    searchText,
    genderFilter,
    fromAge,
    toAge,
    fromDate,
    toDate,
    sortBy,
    sortOrder,
    error,
    errorMessage,
    successMessage,

    // Actions
    fetchPatients: fetchPatientsData,
    search,
    filterByGender,
    filterByAgeRange,
    filterByDateRange,
    changePage,
    changeSorting,
    clearFilters: clearAllFilters,
    clearMessages: clearAllMessages,
  };
};
