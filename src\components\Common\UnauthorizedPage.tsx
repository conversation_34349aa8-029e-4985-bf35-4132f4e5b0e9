import { Button } from '@mui/material';
import React from 'react';
import { useNavigate } from 'react-router-dom';

import { PATHS } from '../../constants/paths';

const UnauthorizedPage: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className='flex flex-col items-center justify-center min-h-screen bg-gray-50 p-4'>
      <div className='bg-white p-8 rounded-lg shadow-md max-w-md w-full text-center'>
        <div className='mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-4'>
          <svg
            className='h-8 w-8 text-red-600'
            fill='none'
            viewBox='0 0 24 24'
            stroke='currentColor'
          >
            <path
              strokeLinecap='round'
              strokeLinejoin='round'
              strokeWidth={2}
              d='M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z'
            />
          </svg>
        </div>
        <h1 className='text-2xl font-bold text-gray-900 mb-2'>Access Denied</h1>
        <p className='text-gray-600 mb-6'>
          You don't have permission to access this page. Please contact your
          administrator if you believe this is an error.
        </p>
        <Button
          onClick={() => navigate(PATHS.LOGIN)}
          className='w-full bg-primary hover:bg-primary/90'
        >
          Go to Login
        </Button>
      </div>
    </div>
  );
};

export default UnauthorizedPage;
