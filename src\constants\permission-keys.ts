// Permission keys based on backend APIPermissions structure
export const permissionKeys = {
  // EMR Module
  emr: {
    access: 'emr.access',
    patientInfo: {
      view: 'emr.patientinfo.view',
      edit: 'emr.patientinfo.edit',
    },
    consultation: {
      view: 'emr.consultation.view',
      create: 'emr.consultation.create',
      edit: 'emr.consultation.edit',
      manage: 'emr.consultation.manage',
    },
    prescriptions: {
      view: 'emr.prescription.view',
      manage: 'emr.prescription.manage',
    },
    reports: {
      manage: 'emr.reports.manage',
    },
    doctorProfile: {
      view: 'emr.doctorprofile.view',
      edit: 'emr.doctorprofile.edit',
    },
    labTest: {
      view: 'emr.lab-test.view',
      manage: 'emr.lab-test.manage',
      search: 'emr.lab-test.search',
    },
    testPackage: {
      view: 'emr.test-package.view',
      manage: 'emr.test-package.manage',
    },
    medicinePackage: {
      view: 'emr.medicine-package.view',
      manage: 'emr.medicine-package.manage',
    },
    lifestyle: {
      manage: 'emr.lifestyle.manage',
    },
    // Department Package uses test-package.manage as per requirement
    departmentPackage: {
      manage: 'emr.test-package.manage',
    },
  },

  // MRD Module
  mrd: {
    access: 'mrd.access',
    patientManagement: {
      view: 'mrd.manage-patient.view',
      edit: 'mrd.manage-patient.edit',
    },
    patientQueue: {
      manage: 'mrd.patient-queue.manage',
    },
  },

  // Admin Module
  admin: {
    dashboard: {
      view: 'dashboard.view',
    },
    roleManagement: {
      manage: 'role.manage',
    },
    permissionManagement: {
      manage: 'permission.manage',
    },
    organizationManagement: {
      manage: 'organization.manage',
    },
  },
};

// API Permissions structure (from your backe
