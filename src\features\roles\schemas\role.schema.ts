import * as yup from 'yup';

export const roleFormSchema = yup.object({
  name: yup
    .string()
    .required('Role name is required')
    .min(2, 'Role name must be at least 2 characters')
    .max(50, 'Role name must not exceed 50 characters')
    .matches(
      /^[a-zA-Z0-9\s_-]+$/,
      'Role name can only contain letters, numbers, spaces, hyphens, and underscores'
    ),
  description: yup
    .string()
    .max(500, 'Description must not exceed 500 characters')
    .notRequired(),
  permissions: yup
    .array()
    .of(
      yup.object({
        id: yup.string().required(),
        module: yup.string().oneOf(['EMR', 'MRD', 'ADMIN']).required(),
        feature: yup.string().required(),
        actions: yup
          .array()
          .of(
            yup
              .string()
              .oneOf(['create', 'read', 'update', 'delete', 'print', 'export'])
              .required()
          )
          .required(),
      })
    )
    .optional(),
});

export type RoleFormSchema = yup.InferType<typeof roleFormSchema>;
