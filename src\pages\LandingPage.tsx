import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';

import { PATHS } from '../constants/paths';
import { msalInstance } from '../utils/msal';
import { apiScopes, b2cPolicies } from '../utils/msalConfig';

const LandingPage: React.FC = (): JSX.Element => {
  const navigate = useNavigate();

  const [isCheckingAuth, setIsCheckingAuth] = useState(true);

  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Check if user is already signed in
        const account = msalInstance.getActiveAccount();
        if (account) {
          // If user is already signed in, the MsalAuthHandler will handle the redirect
          // We'll show a loading state until the redirect happens
          return;
        }
      } catch (error) {
        console.error('Error checking authentication status:', error);
      } finally {
        setIsCheckingAuth(false);
      }
    };

    checkAuth();
  }, [navigate]);

  // Show loading state while checking auth status
  if (isCheckingAuth) {
    return (
      <div className='flex flex-col items-center justify-center min-h-screen bg-gray-50'>
        <div className='animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-blue-600 mb-4'></div>
        <p className='text-gray-600'>Checking authentication status...</p>
      </div>
    );
  }

  const handleLogin = async () => {
    try {
      // Clear any existing accounts to force fresh login
      const accounts = msalInstance.getAllAccounts();
      if (accounts.length > 0) {
        await msalInstance.logout();
      }

      // Always redirect to users page after login
      sessionStorage.setItem('preLoginUrl', PATHS.USERS);

      // Set up the login request with the client ID as scope
      const loginRequest = {
        scopes: [apiScopes.apiRead],
        redirectUri: window.location.origin,
        authority: b2cPolicies.authorities.signUpSignIn.authority,
        prompt: 'select_account',
      };

      // Redirect to Microsoft login
      await msalInstance.loginRedirect(loginRequest);
    } catch (error) {
      if (error instanceof Error) {
        if (error.message.includes('interaction_in_progress')) {
          localStorage.clear();
          sessionStorage.clear();
          window.location.reload();
        } else if (!error.message.includes('AADB2C90091')) {
          // Ignore user cancellation
          console.error('Login error details:', error);
          toast.error('Failed to initiate login. Please try again.');
        }
      } else {
        console.error('Unexpected login error:', error);
        toast.error('An unexpected error occurred during login.');
      }
    }
  };

  return (
    <div className='flex min-h-screen bg-white'>
      {/* Left side with blue pattern */}
      <div className='hidden md:flex md:w-4/6 items-center justify-center bg-[#f0f7ff] relative overflow-hidden'>
        <div className="absolute inset-0 bg-[url('/images/auth-background.png')] bg-cover bg-center"></div>
        <div className='relative z-10 text-center p-8'>
          <img
            src='/images/auth-logo.png'
            alt='ARCA HEALTH SPHERE'
            className='h-34 w-auto mx-auto mb-6'
          />
        </div>
      </div>

      {/* Right side with form */}
      <div className='flex-1 flex flex-col justify-center items-center p-8 max-w-md mx-auto'>
        <div className='w-full space-y-8'>
          <div className='text-center space-y-2'>
            <h2 className='text-3xl font-bold text-gray-900'>
              Welcome to ArcaAI Admin
            </h2>
            <p className='text-gray-500 text-sm'>Sign in to continue</p>
          </div>

          <div className='mt-8 flex flex-col items-center'>
            <button
              onClick={handleLogin}
              className='w-full flex justify-center items-center px-6 py-2 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors'
            >
              Sign In
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LandingPage;
