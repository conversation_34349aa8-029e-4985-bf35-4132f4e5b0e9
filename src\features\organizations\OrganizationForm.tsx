import { yupResolver } from '@hookform/resolvers/yup';
import React, { useEffect } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useDispatch } from 'react-redux';

import AppButton from '../../components/Common/AppButton';
import TextInput from '../../components/Common/MUIInput';
import { AppDispatch } from '../../store';
import {
  createOrganization,
  updateOrganization,
} from '../../store/features/organizations/organization.slice';
import { Organization } from '../../types';
import {
  OrganizationFormData,
  organizationSchema,
} from './validation/organizationSchema';

interface OrganizationFormProps {
  organization?: Organization | null;
  onSuccess: () => void;
  onCancel: () => void;
}

const OrganizationForm: React.FC<OrganizationFormProps> = ({
  organization,
  onSuccess,
  onCancel,
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const defaultValues: OrganizationFormData = {
    name: '',
    contactPersonName: '',
    contactEmail: '',
    contactPhone: null,
    address: {
      street1: '',
      city: '',
      state: '',
      postalCode: '',
      country: '',
    },
    description: null,
  };

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<OrganizationFormData>({
    resolver: yupResolver(organizationSchema),
    defaultValues,
  });

  useEffect(() => {
    if (organization) {
      reset({
        name: organization.name,
        contactPersonName: organization.contactPersonName,
        contactEmail: organization.contactEmail,
        contactPhone: organization.contactPhone || null,
        address: {
          street1: organization.address.street,
          city: organization.address.city,
          state: organization.address.state,
          postalCode: organization.address.postalCode,
          country: organization.address.country,
        },
        description: organization.description || null,
      });
    }
  }, [organization, reset]);

  const onSubmit = async (data: OrganizationFormData) => {
    try {
      const payload = {
        name: data.name,
        contactPersonName: data.contactPersonName,
        contactEmail: data.contactEmail,
        contactPhone: data.contactPhone || '',
        address: {
          street: data.address.street1,
          city: data.address.city,
          state: data.address.state,
          postalCode: data.address.postalCode,
          country: data.address.country,
        },
        description: data.description || '',
      };
      let result;
      if (organization) {
        result = await dispatch(
          updateOrganization({ ...payload, id: organization.id })
        );
      } else {
        result = await dispatch(createOrganization(payload));
      }
      if (result && result.type && result.type.endsWith('fulfilled')) {
        onSuccess();
      }
    } catch {
      // Error is intentionally not used
    }
  };

  return (
    <div className='space-y-6 p-6'>
      <form onSubmit={handleSubmit(onSubmit)} className='space-y-4'>
        <Controller
          name='name'
          control={control}
          render={({ field }) => (
            <TextInput
              {...field}
              label='Organization Name *'
              error={!!errors.name}
              helperText={errors.name?.message}
              placeholder='Enter organization name'
            />
          )}
        />
        <Controller
          name='contactPersonName'
          control={control}
          render={({ field }) => (
            <TextInput
              {...field}
              label='Contact Person *'
              error={!!errors.contactPersonName}
              helperText={errors.contactPersonName?.message}
              placeholder='Enter contact person name'
            />
          )}
        />
        <Controller
          name='contactEmail'
          control={control}
          render={({ field }) => (
            <TextInput
              {...field}
              label='Contact Email *'
              error={!!errors.contactEmail}
              helperText={errors.contactEmail?.message}
              placeholder='Enter contact email'
            />
          )}
        />
        <Controller
          name='contactPhone'
          control={control}
          render={({ field: { onChange, value, ...restField } }) => (
            <TextInput
              {...restField}
              value={value || ''}
              label='Contact Phone'
              error={!!errors.contactPhone}
              helperText={
                errors.contactPhone?.message || 'Enter 10-digit phone number'
              }
              placeholder='Enter 10-digit phone number'
              type='tel'
              inputProps={{
                maxLength: 10,
                inputMode: 'numeric',
                pattern: '\\d{0,10}',
              }}
              onKeyPress={(e) => {
                // Only allow numbers (0-9)
                if (!/[0-9]/.test(e.key)) {
                  e.preventDefault();
                }
              }}
              onChange={(e) => {
                // Remove any non-digit characters and limit to 10 digits
                const newValue = e.target.value.replace(/\D/g, '').slice(0, 10);
                onChange(newValue);
              }}
            />
          )}
        />
        {/* Address Fields */}
        <div className='border-t pt-6'>
          <h3 className='text-lg font-medium text-gray-900 mb-4'>
            Address Information
          </h3>
          <div className='space-y-4'>
            <Controller
              name='address.street1'
              control={control}
              render={({ field }) => (
                <TextInput
                  {...field}
                  label='Street Address *'
                  error={!!errors.address?.street1}
                  helperText={errors.address?.street1?.message}
                  placeholder='Enter street address'
                  multiline
                  minRows={1}
                  maxRows={4}
                />
              )}
            />
            <Controller
              name='address.city'
              control={control}
              render={({ field }) => (
                <TextInput
                  {...field}
                  label='City *'
                  error={!!errors.address?.city}
                  helperText={errors.address?.city?.message}
                  placeholder='Enter city'
                />
              )}
            />
            <Controller
              name='address.state'
              control={control}
              render={({ field }) => (
                <TextInput
                  {...field}
                  label='State *'
                  error={!!errors.address?.state}
                  helperText={errors.address?.state?.message}
                  placeholder='Enter state'
                />
              )}
            />
            <Controller
              name='address.postalCode'
              control={control}
              render={({ field: { onChange, value, ...restField } }) => (
                <TextInput
                  {...restField}
                  value={value || ''}
                  label='Postal Code *'
                  error={!!errors.address?.postalCode}
                  helperText={
                    errors.address?.postalCode?.message ||
                    'Enter 6-digit postal code'
                  }
                  placeholder='Enter 6-digit postal code'
                  type='tel'
                  inputProps={{
                    maxLength: 6,
                    inputMode: 'numeric',
                    pattern: '\\d{0,6}',
                  }}
                  onKeyPress={(e) => {
                    // Only allow numbers (0-9)
                    if (!/[0-9]/.test(e.key)) {
                      e.preventDefault();
                    }
                  }}
                  onChange={(e) => {
                    // Remove any non-digit characters and limit to 6 digits
                    const newValue = e.target.value
                      .replace(/\D/g, '')
                      .slice(0, 6);
                    onChange(newValue);
                  }}
                />
              )}
            />
            <Controller
              name='address.country'
              control={control}
              render={({ field }) => (
                <TextInput
                  {...field}
                  label='Country *'
                  error={!!errors.address?.country}
                  helperText={errors.address?.country?.message}
                  placeholder='Enter country'
                />
              )}
            />
          </div>
        </div>
        {/* Description Field */}
        <Controller
          name='description'
          control={control}
          render={({ field }) => (
            <TextInput
              {...field}
              label='Description'
              error={!!errors.description}
              helperText={errors.description?.message}
              placeholder='Enter description'
              multiline
              rows={3}
            />
          )}
        />
        {/* Form Actions */}
        <div className='flex justify-end space-x-4 pt-4'>
          <AppButton
            type='button'
            kind='secondary'
            onClick={onCancel}
            disabled={isSubmitting}
          >
            Cancel
          </AppButton>
          <AppButton type='submit' kind='primary' disabled={isSubmitting}>
            {isSubmitting
              ? 'Saving...'
              : organization
                ? 'Save Changes'
                : 'Create Organization'}
          </AppButton>
        </div>
      </form>
    </div>
  );
};

export default OrganizationForm;
