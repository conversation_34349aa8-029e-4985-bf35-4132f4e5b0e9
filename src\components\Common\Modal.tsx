import CloseIcon from '@mui/icons-material/Close';
import { Breakpoint } from '@mui/material';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import IconButton from '@mui/material/IconButton';
import React from 'react';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  showCloseButton?: boolean;
}

const sizeMap: Record<string, Breakpoint> = {
  sm: 'xs',
  md: 'sm',
  lg: 'md',
  xl: 'lg',
  full: 'xl',
};

const horizontalPadding = 3; // theme spacing unit (24px)

const Modal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  title,
  children,
  size = 'md',
  showCloseButton = true,
}) => {
  return (
    <Dialog
      open={isOpen}
      onClose={onClose}
      maxWidth={sizeMap[size] || 'sm'}
      fullWidth={size !== 'sm'}
      fullScreen={size === 'full'}
      scroll='body'
    >
      <DialogTitle
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          pr: horizontalPadding,
          pl: horizontalPadding,
          borderBottom: '1px solid #e0e0e0',
        }}
      >
        <span style={{ fontWeight: 600, fontSize: 20 }}>{title}</span>
        {showCloseButton && (
          <IconButton onClick={onClose} size='small'>
            <CloseIcon />
          </IconButton>
        )}
      </DialogTitle>
      <DialogContent
        sx={{
          maxHeight: 'calc(100vh - 200px)',
          overflowY: 'auto',
          paddingTop: 3,
          paddingBottom: 0.5,
          pr: horizontalPadding,
          pl: horizontalPadding,
        }}
      >
        {children}
      </DialogContent>
    </Dialog>
  );
};

export default Modal;
