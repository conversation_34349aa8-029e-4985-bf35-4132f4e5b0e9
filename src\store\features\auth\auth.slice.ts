import {
  createAsyncThunk,
  createSlice,
  isRejected,
  PayloadAction,
  SerializedError,
} from '@reduxjs/toolkit';

import { AuthState, Organization, Role, User } from '../../../types';
import { fetchOrganizationById } from '../organizations/organization.slice';
import authService, { EmrUserInfo } from './auth.service';

// Import RootState type for proper typing
interface RootState {
  organizations: {
    allOrganizations: Organization[];
  };
}

interface ApiErrorResponse {
  data?: {
    message?: string;
  };
}

interface ApiError extends SerializedError {
  response?: ApiErrorResponse;
}

// Try to get selected organization from localStorage if it exists
const getInitialSelectedOrganization = () => {
  try {
    const org = localStorage.getItem('selectedOrganization');
    return org ? JSON.parse(org) : null;
  } catch (error) {
    console.error(
      'Failed to parse selected organization from localStorage',
      error
    );
    return null;
  }
};

// Helper function to save organization to localStorage
const saveOrganizationToLocalStorage = (org: Organization | null) => {
  try {
    if (org) {
      localStorage.setItem('selectedOrganization', JSON.stringify(org));
    } else {
      localStorage.removeItem('selectedOrganization');
    }
  } catch (error) {
    console.error('Failed to save organization to localStorage', error);
  }
};

const initialState: AuthState = {
  user: null,
  selectedOrganization: getInitialSelectedOrganization(),
  currentOrganization: null,
  loading: false,
  error: null,
  successMessage: null,
  isAuthenticated: false,
  isSuperAdmin: false,
  isOrganizationAdmin: false,
  isOrganizationUser: false,
  loggingOut: false,
  emrUserInfo: null,
};

// Create a thunk that doesn't trigger loading state for organization selection
export const selectOrganization = createAsyncThunk(
  'auth/selectOrganization',
  async (organizationId: string, { rejectWithValue, getState, dispatch }) => {
    try {
      // Get organization from the organizations state instead of mock data
      const state = getState() as RootState;
      const allOrganizations = state.organizations.allOrganizations;

      const organization = allOrganizations.find(
        (org: Organization) => org.id === organizationId
      );

      if (!organization) {
        // If not found in the store, try to fetch it
        try {
          const result = await dispatch(fetchOrganizationById(organizationId));
          if (fetchOrganizationById.fulfilled.match(result)) {
            const fetchedOrg = result.payload;
            // Save the fetched organization to localStorage
            localStorage.setItem(
              'selectedOrganization',
              JSON.stringify(fetchedOrg)
            );
            return fetchedOrg;
          }
          throw new Error('Failed to fetch organization');
        } catch (fetchError) {
          console.error('Error fetching organization:', fetchError);
          throw new Error('Organization not found and could not be fetched');
        }
      }

      // Store selected organization in localStorage
      localStorage.setItem(
        'selectedOrganization',
        JSON.stringify(organization)
      );

      // Also update the current organization
      dispatch(setSelectedOrganization(organization));

      return organization;
    } catch (error) {
      console.error('Error in selectOrganization:', error);
      return rejectWithValue(error);
    }
  }
);

export const fetchEmrUserInfo = createAsyncThunk(
  'auth/fetchEmrUserInfo',
  async (email: string, { rejectWithValue }) => {
    try {
      const { user, token } = await authService.fetchEmrUserInfo(email);
      return { user, token };
    } catch (error) {
      const typedError = error as ApiError;
      return rejectWithValue(
        typedError.response?.data?.message ||
          'Failed to fetch user info from EMR'
      );
    }
  }
);

export const logoutUser = createAsyncThunk(
  'auth/logoutUser',
  async (_, { dispatch }) => {
    try {
      // Set loggingOut flag to true
      dispatch(setLoggingOut(true));

      // Import the MSAL logout function and resetRedirectState
      const { logout } = await import('../../../utils/msal');
      const { resetRedirectState } = await import(
        '../../../utils/redirect-utils'
      );

      // Clear all storage first
      localStorage.clear();
      sessionStorage.clear();

      // Clear cookies by setting expiration to past date
      document.cookie.split(';').forEach((cookie) => {
        const [name] = cookie.trim().split('=');
        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
      });

      // Reset the auth state
      dispatch({ type: 'RESET_ALL_SLICES' });

      // Reset the redirect state
      resetRedirectState();

      try {
        // Perform MSAL logout with redirect to login page
        await logout();

        // If we get here, the logout was successful but we should still redirect
        window.location.href = '/login';
      } catch (msalError) {
        console.error('MSAL logout error:', msalError);
        // Even if MSAL logout fails, we should still redirect to login
        window.location.href = '/login';
      }

      return true;
    } catch (error) {
      console.error('Error during logout:', error);
      // Even if there's an error, we should still clear everything and redirect
      localStorage.clear();
      sessionStorage.clear();
      window.location.href = '/login';
      return false;
    }
  }
);

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setLoggingOut(state, action: PayloadAction<boolean>) {
      state.loggingOut = action.payload;
    },
    setAuthState: (
      state,
      action: PayloadAction<{
        user: User;
        selectedOrganization: Organization | null;
        currentOrganization?: Organization | null;
      }>
    ) => {
      state.user = action.payload.user;
      state.isAuthenticated = true;

      // Set currentOrganization if provided
      if (action.payload.currentOrganization) {
        state.currentOrganization = action.payload.currentOrganization;
      }

      // If no organization is provided but user is organization admin, use their organization
      if (
        !action.payload.selectedOrganization &&
        action.payload.user.organization
      ) {
        const isOrgAdmin = action.payload.user.roles?.some(
          (role: Role) =>
            role.name.toUpperCase() === 'ADMIN' ||
            role.name.toLowerCase() === 'admin' ||
            role.name.toLowerCase() === 'administrator'
        );
        const isSuperAdminRole = action.payload.user.roles?.some(
          (role: Role) =>
            role.name.toUpperCase() === 'SUPER_ADMIN' ||
            role.name.toLowerCase() === 'super admin' ||
            role.name.toLowerCase() === 'superadmin'
        );
        if (isOrgAdmin && !isSuperAdminRole) {
          state.selectedOrganization = action.payload.user.organization;
        } else {
          state.selectedOrganization = action.payload.selectedOrganization;
        }
      } else {
        state.selectedOrganization = action.payload.selectedOrganization;
      }
    },
    clearError: (state) => {
      state.error = null;
    },
    clearMessages: (state) => {
      state.error = null;
      state.successMessage = null;
    },
    clearSuccessMessage: (state) => {
      state.successMessage = null;
    },
    setSelectedOrganization: (
      state,
      action: PayloadAction<Organization | null>
    ) => {
      state.selectedOrganization = action.payload;
      saveOrganizationToLocalStorage(action.payload);
    },
    clearSelectedOrganization: (state) => {
      state.selectedOrganization = null;
      localStorage.removeItem('selectedOrganization');
    },
    setEmrUserInfo(state, action: PayloadAction<EmrUserInfo | null>) {
      state.emrUserInfo = action.payload;
    },
  },
  extraReducers: (builder) => {
    // Handle fetchEmrUserInfo
    builder
      .addCase(fetchEmrUserInfo.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(
        fetchEmrUserInfo.fulfilled,
        (
          state,
          action: PayloadAction<{ user: EmrUserInfo; token: string }>
        ) => {
          state.loading = false;
          state.emrUserInfo = action.payload.user;
          // Store the token if needed
          if (action.payload.token) {
            localStorage.setItem('token', action.payload.token);
          }
        }
      )
      .addCase(fetchEmrUserInfo.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // Other reducers
    builder

      // Select Organization specific cases
      .addCase(selectOrganization.fulfilled, (state, action) => {
        state.selectedOrganization = action.payload;
        state.error = null; // Clear any previous errors
        // Persist the selected organization to localStorage
        localStorage.setItem(
          'selectedOrganization',
          JSON.stringify(action.payload)
        );
        // Also update the current organization if it's not set
        if (!state.currentOrganization) {
          state.currentOrganization = action.payload;
        }
      })

      // Handle fetchOrganizationById fulfilled case to set currentOrganization for organization admins
      .addCase(fetchOrganizationById.fulfilled, (state, action) => {
        // Skip for super admins
        const isSuperAdmin = state.user?.roles?.some(
          (role: Role) =>
            role.name.toUpperCase() === 'SUPER_ADMIN' ||
            role.name.toLowerCase() === 'super admin' ||
            role.name.toLowerCase() === 'superadmin'
        );

        if (isSuperAdmin) {
          return; // Skip organization setting for super admins
        }

        // Set currentOrganization for non-super admin users when organization is fetched
        state.currentOrganization = action.payload;
        localStorage.setItem(
          'currentOrganization',
          JSON.stringify(action.payload)
        );

        // Also set selectedOrganization for organization admins
        if (state.user) {
          const isOrgAdmin = state.user.roles?.some(
            (role: Role) =>
              role.name.toUpperCase() === 'ADMIN' ||
              role.name.toLowerCase() === 'admin' ||
              role.name.toLowerCase() === 'administrator'
          );

          if (isOrgAdmin) {
            state.selectedOrganization = action.payload;
            localStorage.setItem(
              'selectedOrganization',
              JSON.stringify(action.payload)
            );
          }
        }
      })

      // Logout specific case
      .addCase(logoutUser.fulfilled, (state) => {
        state.isAuthenticated = false;
        state.user = null;
        state.currentOrganization = null;
        state.selectedOrganization = null;
        state.loggingOut = false;
      })
      .addCase(logoutUser.pending, (state) => {
        state.loggingOut = true;
      })

      // Generic matchers for other actions
      // Removed loading state for selectOrganization since it's not an API call
      .addMatcher(isRejected(selectOrganization), (state, action) => {
        const error = action.payload as ApiError;
        state.error =
          error?.response?.data?.message ||
          error?.message ||
          'An error occurred';
      });
  },
});

export const {
  setAuthState,
  clearError,
  clearMessages,
  clearSuccessMessage,
  setSelectedOrganization,
  clearSelectedOrganization,
  setEmrUserInfo,
  setLoggingOut,
} = authSlice.actions;
export default authSlice.reducer;
