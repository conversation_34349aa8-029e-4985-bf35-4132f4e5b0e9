import { useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { AppDispatch, RootState } from '../../../store';
import {
  clearError,
  clearMessages,
  clearSelection,
  clearSuccessMessage,
  fetchMedicines,
  removeMedicines as removeMedicinesAction,
  selectAllMedicines,
  selectMedicine,
  setIsActiveFilter,
  setLimit,
  setSearchText,
  updateMedicineInList,
  updateMedicines,
} from '../../../store/features/medicines/medicine.slice';
import {
  MedicineListItem,
  MedicineListParams,
  UpdateMedicineRequest,
} from '../types/medicine.types';

export const useMedicines = (organizationId?: string) => {
  const dispatch = useDispatch<AppDispatch>();
  const {
    medicines,
    loading,
    updating,
    error,
    successMessage,
    total,
    page,
    limit,
    searchText,
    isActiveFilter,
    selectedMedicines,
    isAllSelected,
    continuationToken,
    hasMoreResults,
    totalFetched,
    totalPages,
  } = useSelector((state: RootState) => state.medicines);

  const fetchMedicinesList = useCallback(
    (customParams?: Partial<MedicineListParams>) => {
      if (!organizationId) return;
      const params: MedicineListParams = {
        organizationId,
        page,
        pageSize: limit,
        ...(searchText && { searchText }),
        ...(isActiveFilter !== 'all' && {
          isActive: isActiveFilter === 'active',
        }),
        ...customParams,
      };
      dispatch(fetchMedicines(params));
    },
    [dispatch, organizationId, page, limit, searchText, isActiveFilter]
  );

  const updateMedicinesList = useCallback(
    async (data: UpdateMedicineRequest) => {
      const result = await dispatch(updateMedicines(data));
      if (updateMedicines.fulfilled.match(result)) {
        fetchMedicinesList();
      }
      return result;
    },
    [dispatch, fetchMedicinesList]
  );

  const search = useCallback(
    (searchTerm: string) => {
      dispatch(setSearchText(searchTerm));
    },
    [dispatch]
  );

  const filterByStatus = useCallback(
    (status: string) => {
      dispatch(setIsActiveFilter(status));
    },
    [dispatch]
  );

  const handlePageChange = useCallback(
    (newPage: number) => {
      if (newPage > page && hasMoreResults && continuationToken) {
        fetchMedicinesList({ continuationToken });
      } else {
        fetchMedicinesList({ page: newPage });
      }
    },
    [fetchMedicinesList, hasMoreResults, continuationToken, page]
  );

  const changeLimit = useCallback(
    (newLimit: number) => {
      dispatch(setLimit(newLimit));
    },
    [dispatch]
  );

  const toggleMedicineSelection = useCallback(
    (medicineId: string) => {
      dispatch(selectMedicine(medicineId));
    },
    [dispatch]
  );

  const toggleSelectAll = useCallback(() => {
    dispatch(selectAllMedicines());
  }, [dispatch]);

  const clearMedicineSelection = useCallback(() => {
    dispatch(clearSelection());
  }, [dispatch]);

  const updateMedicineInListLocal = useCallback(
    (medicineId: string, updates: Partial<MedicineListItem>) => {
      dispatch(updateMedicineInList({ medicineId, updates }));
    },
    [dispatch]
  );

  const clearErrorMessage = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  const clearSuccessMsg = useCallback(() => {
    dispatch(clearSuccessMessage());
  }, [dispatch]);

  const clearAllMessages = useCallback(() => {
    dispatch(clearMessages());
  }, [dispatch]);

  const removeMedicines = useCallback(async () => {
    if (!organizationId)
      return { success: false, message: 'No organization selected' };

    const request = isAllSelected
      ? { organizationId, selectAll: true }
      : { organizationId, medicines: selectedMedicines };

    const result = await dispatch(removeMedicinesAction(request));

    if (removeMedicinesAction.fulfilled.match(result)) {
      fetchMedicinesList();
    }

    return result.payload;
  }, [
    dispatch,
    organizationId,
    selectedMedicines,
    isAllSelected,
    fetchMedicinesList,
  ]);

  useEffect(() => {
    if (organizationId) {
      fetchMedicinesList();
    }

    // Clear success message on unmount
    return () => {
      if (successMessage) {
        dispatch(clearSuccessMessage());
      }
    };
  }, [fetchMedicinesList, organizationId, dispatch, successMessage]);

  return {
    medicines,
    loading,
    updating,
    error,
    successMessage,
    total,
    page,
    limit,
    searchText,
    isActiveFilter,
    selectedMedicines,
    isAllSelected,
    continuationToken,
    hasMoreResults,
    totalFetched,
    totalPages,
    fetchMedicinesList,
    updateMedicinesList,
    search,
    filterByStatus,
    changePage: handlePageChange,
    changeLimit,
    toggleMedicineSelection,
    toggleSelectAll,
    clearMedicineSelection,
    updateMedicineInListLocal,
    clearErrorMessage,
    clearSuccessMsg,
    clearAllMessages,
    removeMedicines,
  };
};
