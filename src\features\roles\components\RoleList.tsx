import { Plus, <PERSON><PERSON>heck } from 'lucide-react';
import React, { memo, useCallback, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';

import AppButton from '../../../components/Common/AppButton';
import ConfirmationModal from '../../../components/Common/ConfirmationModal';
import DataTable, { Column } from '../../../components/Common/DataTable';
import Modal from '../../../components/Common/Modal';
import SearchBar from '../../../components/Common/SearchBar';
import { useConfirmation } from '../../../hooks/useConfirmation';
import { RootState } from '../../../store';
import { useRoles } from '../hooks/useRoles';
import { RoleFormSchema } from '../schemas/role.schema';
import { Role } from '../types/role.types';
import RoleForm from './RoleForm';
import RolePermissionManager from './RolePermissionManager';

interface RoleListProps {
  organizationName: string;
  organizationId?: string;
}

const RoleList: React.FC<RoleListProps> = memo(
  ({ organizationName, organizationId }) => {
    const [isFormOpen, setIsFormOpen] = useState(false);
    const [roleToEdit, setRoleToEdit] = useState<Role | null>(null);
    const [showPermissionModal, setShowPermissionModal] = useState(false);
    const [roleForPermissions, setRoleForPermissions] = useState<Role | null>(
      null
    );

    // Use the confirmation hook instead of custom modal state
    const confirmation = useConfirmation();

    const { users } = useSelector((state: RootState) => state.users);

    const {
      roles,
      loading,
      total,
      page,
      limit,
      searchName,
      fetchRoles,
      createRole,
      updateRole,
      deleteRole,
      search,
      changePage,
      clearMessages,
      clearUpdateSuccess,
    } = useRoles(organizationId); // Pass organizationId for super admins, fallback to localStorage for org admins

    // Memoized columns definition
    const columns: Column<Role>[] = useMemo(
      () => [
        {
          key: 'name',
          label: 'Role Name',
          render: (name) => (
            <div className='flex items-center space-x-3'>
              <div className='flex-shrink-0'>
                <div className='w-10 h-10 bg-green-100 rounded-full flex items-center justify-center'>
                  <ShieldCheck className='w-5 h-5 text-green-600' />
                </div>
              </div>
              <div>
                <div className='font-medium text-gray-900'>{name}</div>
              </div>
            </div>
          ),
        },
        {
          key: 'description',
          label: 'Description',
          render: (description) => (
            <div className='text-sm text-gray-700'>
              {description || 'No description provided'}
            </div>
          ),
        },
        {
          key: 'isDefault',
          label: 'System Role',
          render: (_, role) => (
            <span
              className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                role.isDefault || role.isSystem
                  ? 'bg-gray-200 text-gray-800'
                  : 'bg-blue-100 text-blue-800'
              }`}
            >
              {role.isDefault || role.isSystem ? 'Yes' : 'No'}
            </span>
          ),
        },
      ],
      []
    );

    // Event handlers
    const handleOpenForm = useCallback(
      (role?: Role) => {
        setRoleToEdit(role || null);
        setIsFormOpen(true);
        clearMessages();
      },
      [clearMessages]
    );

    const handleCloseForm = useCallback(() => {
      setIsFormOpen(false);
      setRoleToEdit(null);
      clearUpdateSuccess();
    }, [clearUpdateSuccess]);

    const handleDelete = useCallback(
      (role: Role) => {
        // Check if role can be deleted
        const isSystemRole = role.isDefault || role.isSystem;
        const roleInUse = users.some((user) =>
          user.roles.some((userRole) => userRole.id === role.id)
        );

        if (isSystemRole) {
          confirmation.confirmAction(
            'Cannot Delete Role',
            'This is a system role and cannot be deleted.',
            () => {}, // No action needed
            {
              confirmText: 'OK',
              type: 'warning',
              itemName: role.name,
            }
          );
          return;
        }

        if (roleInUse) {
          confirmation.confirmAction(
            'Cannot Delete Role',
            'This role is assigned to one or more users and cannot be deleted.',
            () => {}, // No action needed
            {
              confirmText: 'OK',
              type: 'warning',
              itemName: role.name,
            }
          );
          return;
        }

        // Role can be deleted
        confirmation.confirmDelete(
          role.name,
          async () => {
            await deleteRole(role.id);
            fetchRoles(); // Refresh the list after deletion
          },
          `Are you sure you want to delete "${role.name}"? This action cannot be undone and will permanently remove all associated data.`
        );
      },
      [confirmation, deleteRole, fetchRoles, users]
    );

    const handleOpenPermissionManager = useCallback((role: Role) => {
      setRoleForPermissions(role);
      setShowPermissionModal(true);
    }, []);

    const handleClosePermissionManager = useCallback(() => {
      setShowPermissionModal(false);
      setRoleForPermissions(null);
    }, []);

    const handleSubmit = useCallback(
      async (data: RoleFormSchema) => {
        try {
          if (roleToEdit) {
            const updateData: Parameters<typeof updateRole>[0] = {
              id: roleToEdit.id,
              name: data.name,
              organizationId: organizationId || '',
            };
            if (data.description) {
              updateData.description = data.description;
            }
            // Note: permissions are handled by a separate API, not included in update
            await updateRole(updateData);
          } else {
            const createData: Parameters<typeof createRole>[0] = {
              name: data.name,
              organizationId: organizationId || '',
            };
            if (data.description) {
              createData.description = data.description;
            }
            // Note: permissions are handled by a separate API, not included in create
            await createRole(createData);
          }
        } catch {
          // Handle error silently or show user feedback
        }
      },
      [roleToEdit, updateRole, createRole, organizationId]
    );

    const handleSearchChange = useCallback(
      (searchTerm: string) => {
        search(searchTerm);
      },
      [search]
    );

    const handlePageChange = useCallback(
      (newPage: number) => {
        changePage(newPage);
      },
      [changePage]
    );

    const handleFormSuccess = useCallback(() => {
      handleCloseForm();
      fetchRoles();
    }, [handleCloseForm, fetchRoles]);

    // Note: API calls are now handled by useNavigationApiTrigger in App.tsx
    // This ensures fresh data on every navigation to the roles page

    // Get conditional actions for each role
    const getActionsForRole = useCallback(
      (role: Role) => {
        const actions = [];
        const isSystemRole = role.isDefault || role.isSystem;

        // Add permission management action for all roles
        actions.push({
          label: 'Manage Permissions',
          onClick: () => handleOpenPermissionManager(role),
          color: 'primary' as const,
        });

        // Add edit and delete actions only for non-system roles
        if (!isSystemRole) {
          actions.push(
            {
              label: 'Edit',
              onClick: () => handleOpenForm(role),
              color: 'secondary' as const,
            },
            {
              label: 'Delete',
              onClick: () => handleDelete(role),
              color: 'danger' as const,
            }
          );
        }

        return actions;
      },
      [handleOpenPermissionManager, handleOpenForm, handleDelete]
    );

    return (
      <div className='space-y-6'>
        <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4'>
          <div>
            <h1 className='text-2xl font-bold text-gray-900'>Roles</h1>
            <p className='mt-1 text-sm text-gray-500'>
              Manage roles for {organizationName}
            </p>
          </div>
          <div className='flex items-center gap-3'>
            <SearchBar
              value={searchName}
              onChange={handleSearchChange}
              placeholder='Search roles...'
              size='md'
            />
            <AppButton
              onClick={() => handleOpenForm()}
              startIcon={<Plus className='w-4 h-4' />}
            >
              Add Role
            </AppButton>
          </div>
        </div>

        <DataTable<Role>
          columns={columns}
          data={roles}
          loading={loading}
          pagination={{
            total,
            page,
            limit,
            onPageChange: handlePageChange,
          }}
          actions={getActionsForRole}
          // onDelete={handleDelete}
          // onEdit={handleOpenForm}
        />

        <Modal
          isOpen={isFormOpen}
          onClose={handleCloseForm}
          title={roleToEdit ? 'Edit Role' : 'Create Role'}
          size='sm'
        >
          <RoleForm
            role={roleToEdit}
            onSubmit={handleSubmit}
            onSuccess={handleFormSuccess}
            onCancel={handleCloseForm}
          />
        </Modal>

        <Modal
          isOpen={showPermissionModal}
          onClose={handleClosePermissionManager}
          title={`Manage Role Permissions - ${roleForPermissions?.name}`}
          showCloseButton={true}
          size='md'
        >
          {roleForPermissions && (
            <RolePermissionManager
              roleId={roleForPermissions.id}
              roleName={roleForPermissions.name}
              onClose={handleClosePermissionManager}
            />
          )}
        </Modal>

        {/* Confirmation Modal */}
        <ConfirmationModal
          isOpen={confirmation.isOpen}
          onClose={confirmation.hideConfirmation}
          onConfirm={confirmation.handleConfirm}
          title={confirmation.title}
          message={confirmation.message}
          confirmText={confirmation.confirmText || 'Confirm'}
          cancelText={confirmation.cancelText || 'Cancel'}
          type={confirmation.type || 'danger'}
          loading={confirmation.loading}
          {...(confirmation.itemName && { itemName: confirmation.itemName })}
        />
      </div>
    );
  }
);

RoleList.displayName = 'RoleList';

export default RoleList;
