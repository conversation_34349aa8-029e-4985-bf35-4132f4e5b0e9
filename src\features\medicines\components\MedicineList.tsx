import { Trash2 } from 'lucide-react';
import React, { useCallback, useMemo, useState } from 'react';

import AppButton from '../../../components/Common/AppButton';
import ConfirmationModal from '../../../components/Common/ConfirmationModal';
import DataTable, { Column } from '../../../components/Common/DataTable';
import Modal from '../../../components/Common/Modal';
import SearchBar from '../../../components/Common/SearchBar';
import StatusBadge from '../../../components/Common/StatusBadge';
import Tooltip from '../../../components/Common/Tooltip';
import { useMedicines } from '../hooks/useMedicines';
import { MedicineListItem } from '../types/medicine.types';
import MedicineForm from './MedicineForm';

interface MedicineListProps {
  organizationName: string;
  organizationId: string;
}

const MedicineList: React.FC<MedicineListProps> = ({
  organizationName,
  organizationId: currentOrgId,
}): JSX.Element => {
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingMedicine, setEditingMedicine] =
    useState<MedicineListItem | null>(null);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const {
    medicines,
    loading,
    total,
    page,
    limit,
    searchText,
    selectedMedicines,
    isAllSelected,
    search,
    changePage,
    toggleMedicineSelection,
    toggleSelectAll,
    clearMedicineSelection,
    updateMedicinesList,
    removeMedicines,
  } = useMedicines(currentOrgId);

  // Clean up any remaining references to selectedOrganization or organization
  // const selectedOrganization = { id: currentOrgId, name: organizationName };

  // Removed auto-selection of active medicines to allow manual selection

  // Bulk update handler
  const handleBulkUpdate = useCallback(async () => {
    if (!currentOrgId || (!selectedMedicines.length && !isAllSelected)) {
      return;
    }
    if (isAllSelected) {
      await updateMedicinesList({
        organizationId: currentOrgId,
        selectAll: true,
      });
    } else {
      const selectedData = medicines.filter((m) =>
        selectedMedicines.includes(m.medicineId)
      );
      await updateMedicinesList({
        organizationId: currentOrgId,
        medicines: selectedData.map((m) => ({
          medicineId: m.medicineId,
          isActive: m.isActive,
          price: m.organizationPrice ?? m.defaultCost ?? 0,
        })),
      });
    }
    clearMedicineSelection();
  }, [
    currentOrgId,
    selectedMedicines,
    isAllSelected,
    medicines,
    updateMedicinesList,
    clearMedicineSelection,
  ]);

  // Remove medicines with confirmation modal
  const handleRemove = useCallback(async () => {
    if (!currentOrgId || (!selectedMedicines.length && !isAllSelected)) {
      return;
    }
    setShowConfirmModal(true);
  }, [currentOrgId, selectedMedicines, isAllSelected]);

  const confirmRemove = useCallback(async () => {
    try {
      await removeMedicines();
      setShowConfirmModal(false);
    } catch (error) {
      console.error(error);
    }
  }, [removeMedicines]);

  const cancelRemove = useCallback(() => setShowConfirmModal(false), []);

  const { activeCount, inactiveCount } = useMemo(() => {
    if (isAllSelected) {
      const selectedCount = selectedMedicines.length || total;
      return { activeCount: selectedCount, inactiveCount: selectedCount };
    }

    const active = medicines.filter(
      (medicine) =>
        selectedMedicines.includes(medicine.medicineId) && medicine.isActive
    ).length;
    const inactive = medicines.filter(
      (medicine) =>
        selectedMedicines.includes(medicine.medicineId) && !medicine.isActive
    ).length;

    return { activeCount: active, inactiveCount: inactive };
  }, [selectedMedicines, medicines, isAllSelected, total]);

  const handleEdit = useCallback((medicine: MedicineListItem) => {
    setEditingMedicine(medicine);
    setShowEditModal(true);
  }, []);

  // Columns
  const columns: Column<MedicineListItem>[] = useMemo(
    () => [
      {
        key: 'selection',
        label: (
          <input
            type='checkbox'
            checked={
              isAllSelected ||
              (selectedMedicines.length > 0 &&
                selectedMedicines.length === medicines.length)
            }
            onChange={toggleSelectAll}
            className='rounded border-gray-300 text-blue-600 focus:ring-blue-500'
          />
        ),
        render: (_, medicine) => (
          <input
            type='checkbox'
            checked={
              selectedMedicines.includes(medicine.medicineId) || isAllSelected
            }
            onChange={() => toggleMedicineSelection(medicine.medicineId)}
            disabled={isAllSelected}
            className={`rounded border-gray-300 text-blue-600 focus:ring-blue-500 ${
              isAllSelected ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
            }`}
          />
        ),
        width: '40px',
      },
      {
        key: 'productName',
        label: 'Product Name',
        render: (_, row) => (
          <Tooltip content={row.productName || row.name || '-'}>
            <span className='w-[180px] overflow-hidden whitespace-nowrap truncate block cursor-help text-sm font-medium text-gray-900'>
              {row.productName || row.name || '-'}
            </span>
          </Tooltip>
        ),
        width: '180px',
      },
      {
        key: 'mrp',
        label: 'Cost',
        render: (_, row) => (
          <span className='text-sm font-medium text-gray-900'>
            {row.mrp !== undefined ? `₹${row.mrp}` : '-'}
          </span>
        ),
        width: '80px',
      },
      {
        key: 'organizationCost',
        label: 'Organization Cost',
        render: (_, row) => {
          const price = row.price ?? row.organizationPrice ?? row.defaultCost;
          return (
            <span className='text-blue-600 font-medium'>
              {price !== undefined ? `₹${price}` : '-'}
            </span>
          );
        },
        width: '120px',
      },
      {
        key: 'qty',
        label: 'Quantity',
        render: (_, row) => row.qty || '-',
        width: '80px',
      },
      {
        key: 'productForm',
        label: 'Form',
        render: (_, row) => (
          <span className='truncate block'>{row.productForm || '-'}</span>
        ),
        width: '80px',
      },
      {
        key: 'medicineType',
        label: 'Type',
        render: (_, row) => (
          <span className='truncate block'>{row.medicineType || '-'}</span>
        ),
        width: '80px',
      },
      {
        key: 'isActive',
        label: 'Status',
        render: (_, row) => (
          <StatusBadge status={row.isActive ? 'active' : 'inactive'} />
        ),
        width: '80px',
      },
    ],
    [
      isAllSelected,
      selectedMedicines,
      medicines.length,
      toggleSelectAll,
      toggleMedicineSelection,
    ]
  );

  // Organization requirements are now handled by the parent component

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4'>
        <div>
          <h1 className='text-2xl font-bold text-gray-900'>Medicines</h1>
          <p className='mt-1 text-sm text-gray-500'>
            Manage medicines for {organizationName}
          </p>
        </div>
        <div className='flex items-center space-x-3'>
          <SearchBar
            value={searchText}
            onChange={search}
            placeholder='Search medicines...'
            size='md'
          />
          {(selectedMedicines.length > 0 || isAllSelected) && (
            <div className='flex items-center gap-4'>
              <AppButton
                onClick={handleBulkUpdate}
                disabled={
                  (!selectedMedicines.length && !isAllSelected) ||
                  loading ||
                  inactiveCount === 0
                }
                kind='primary'
                startIcon={
                  loading ? (
                    <div className='w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin' />
                  ) : null
                }
              >
                {loading
                  ? `Updating... ${inactiveCount > 0 ? `(${inactiveCount})` : ''}`
                  : `Bulk Update${inactiveCount > 0 ? ` (${inactiveCount})` : ''}`}
              </AppButton>
              <AppButton
                onClick={handleRemove}
                disabled={
                  (!selectedMedicines.length && !isAllSelected) ||
                  loading ||
                  activeCount === 0
                }
                kind='primary'
                startIcon={
                  loading ? (
                    <div className='w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin' />
                  ) : (
                    <Trash2 className='w-4 h-4' />
                  )
                }
                style={{
                  backgroundColor:
                    (!selectedMedicines.length && !isAllSelected) ||
                    loading ||
                    activeCount === 0
                      ? 'rgba(0, 0, 0, 0.12)'
                      : '#dc2626',
                }}
              >
                Remove{activeCount > 0 ? ` (${activeCount})` : ''}
              </AppButton>
            </div>
          )}
        </div>
      </div>
      {/* Status Filter below heading */}

      {/* Data Table */}
      <DataTable
        columns={columns}
        data={medicines}
        loading={loading}
        // Status filter removed as per requirements
        // searchFilters={
        //   <div className='flex items-center space-x-3'>
        //     <SelectInput
        //       value={isActiveFilter}
        //       onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
        //         filterByStatus(e.target.value)
        //       }
        //       options={statusFilterOptions}
        //       size='small'
        //       sx={{ minWidth: 120 }}
        //     />
        //   </div>
        // }
        pagination={{
          total,
          page,
          limit,
          onPageChange: changePage,
        }}
        onEdit={handleEdit}
      />
      {/* Edit Modal */}
      <Modal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        title='Edit Medicine'
        size='md'
      >
        <div className='px-4 pb-4'>
          <MedicineForm
            medicine={editingMedicine}
            onSuccess={() => setShowEditModal(false)}
            onClose={() => setShowEditModal(false)}
            organizationId={currentOrgId} // Using the passed organizationId prop
          />
        </div>
      </Modal>
      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={showConfirmModal}
        onClose={cancelRemove}
        onConfirm={confirmRemove}
        title='Confirm Removal'
        message={
          isAllSelected
            ? 'Are you sure you want to remove all selected medicines? This action cannot be undone.'
            : `Are you sure you want to remove ${selectedMedicines.length} selected medicine(s)?`
        }
        confirmText='Remove'
        cancelText='Cancel'
        type='danger'
        loading={loading}
      />
    </div>
  );
};

export default MedicineList;
