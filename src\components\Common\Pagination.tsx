import Box from '@mui/material/Box';
import MuiPagination from '@mui/material/Pagination';
import Typography from '@mui/material/Typography';
import React from 'react';

interface PaginationProps {
  page: number;
  limit: number;
  total: number;
  onPageChange: (event: React.ChangeEvent<unknown>, page: number) => void;
  className?: string;
}

const Pagination: React.FC<PaginationProps> = ({
  page,
  limit,
  total,
  onPageChange,
  className = '',
}) => {
  const totalPages = Math.ceil(total / limit);
  const startItem = total === 0 ? 0 : (page - 1) * limit + 1;
  const endItem = Math.min(page * limit, total);

  return (
    <Box
      className={className}
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        px: 3,
        py: 2,
        bgcolor: 'background.paper',
        borderTop: '1px solid',
        borderColor: 'divider',
      }}
    >
      <Typography variant='body2' color='text.secondary'>
        Showing {startItem} to {endItem} of {total} results
      </Typography>
      <MuiPagination
        page={page}
        count={totalPages}
        onChange={onPageChange}
        color='primary'
        shape='rounded'
        siblingCount={0}
        boundaryCount={1}
        size='small'
      />
    </Box>
  );
};

export default Pagination;
