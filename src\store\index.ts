import {
  combineReducers,
  configureStore,
  createReducer,
} from '@reduxjs/toolkit';

// Import all slices
import authSlice from './features/auth/auth.slice';
import labTestSlice from './features/lab-tests/labTest.slice';
import medicineSlice from './features/medicines/medicine.slice';
import organizationSlice from './features/organizations/organization.slice';
import patientSlice from './features/patients/patient.slice';
import permissionSlice from './features/permissions/permission.slice';
import roleSlice from './features/roles/role.slice';
import userSlice from './features/users/user.slice';
import { toastMiddleware } from './middleware/toastMiddleware';
import {
  clearAllMessages,
  clearErrorMessage,
  clearSuccessMessage,
} from './rootActions';
import auditSlice from './slices/auditSlice';
import bankSlice from './slices/bankSlice';
import branchSlice from './slices/branchSlice';
import departmentSlice from './slices/departmentSlice';
import languageSlice from './slices/languageSlice';
import masterDataSlice from './slices/masterDataSlice';
import templatesReducer from './slices/templateSlice';
import vitalSlice from './slices/vitalSlice';

// Define the app state interface
type AppState = {
  [key: string]: {
    successMessage?: string | null;
    error?: string | null;
    errorMessage?: string | null;
    [key: string]: unknown;
  };
};

// Root reducer that handles common actions
const rootReducer = createReducer({} as AppState, (builder) => {
  builder
    .addCase(clearSuccessMessage, (state, action) => {
      const { slice } = action.payload;
      if (state[slice] && 'successMessage' in state[slice]) {
        // Clear success message for the specified slice
        state[slice].successMessage = null;
      }
    })
    .addCase(clearErrorMessage, (state, action) => {
      const { slice } = action.payload;
      if (state[slice]) {
        // Clear both error and errorMessage for the specified slice
        if ('error' in state[slice]) {
          state[slice].error = null;
        }
        if ('errorMessage' in state[slice]) {
          state[slice].errorMessage = null;
        }
      }
    })
    .addCase(clearAllMessages, (state, action) => {
      const { slice } = action.payload;
      if (state[slice]) {
        // Clear all messages for the specified slice
        if ('successMessage' in state[slice]) {
          state[slice].successMessage = null;
        }
        if ('error' in state[slice]) {
          state[slice].error = null;
        }
        if ('errorMessage' in state[slice]) {
          state[slice].errorMessage = null;
        }
      }
    });
});

// Combine all reducers
const appReducer = combineReducers({
  _root: rootReducer,
  auth: authSlice,
  labTests: labTestSlice,
  medicines: medicineSlice,
  organizations: organizationSlice,
  patients: patientSlice,
  users: userSlice,
  roles: roleSlice,
  permissions: permissionSlice,
  departments: departmentSlice,
  branches: branchSlice,
  banks: bankSlice,
  languages: languageSlice,
  vitals: vitalSlice,
  templates: templatesReducer,
  masterData: masterDataSlice,
  audit: auditSlice,
});

export const store = configureStore({
  reducer: appReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }).concat(toastMiddleware),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Re-export the message clearing actions
export {
  clearAllMessages,
  clearErrorMessage,
  clearSuccessMessage,
} from './rootActions';
