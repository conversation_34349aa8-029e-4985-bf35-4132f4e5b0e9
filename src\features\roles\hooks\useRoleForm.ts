import { yup<PERSON>esolver } from '@hookform/resolvers/yup';
import { useCallback, useEffect } from 'react';
import { useForm } from 'react-hook-form';

import { Permission } from '../../../types';
import { RoleFormSchema, roleFormSchema } from '../schemas/role.schema';
import { Role } from '../types/role.types';

interface UseRoleFormProps {
  role?: Role | null;
  onSubmit: (data: RoleFormSchema) => void | Promise<void>;
  onSuccess?: () => void;
}

export const useRoleForm = ({
  role,
  onSubmit,
  onSuccess,
}: UseRoleFormProps) => {
  const form = useForm({
    resolver: yupResolver(roleFormSchema),
    defaultValues: {
      name: '',
      description: undefined,
      permissions: undefined,
    },
    mode: 'onChange',
  });

  const {
    control,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors, isSubmitting, isValid, isDirty },
  } = form;

  // Watch form values for real-time updates
  const watchedValues = watch();

  // Reset form when role changes
  useEffect(() => {
    if (role) {
      reset({
        name: role.name || '',
        description: role.description || undefined,
        permissions: role.permissions || undefined,
      });
    } else {
      reset({
        name: '',
        description: undefined,
        permissions: undefined,
      });
    }
  }, [role, reset]);

  // Handle form submission
  const handleFormSubmit = useCallback(
    async (data: RoleFormSchema) => {
      try {
        await onSubmit(data);
        onSuccess?.();
      } catch {
        // Handle error silently or show user feedback
      }
    },
    [onSubmit, onSuccess]
  );

  // Update permissions
  const updatePermissions = useCallback(
    (permissions: Permission[]) => {
      // Filter permissions to only include those with required properties for the form
      const formPermissions = permissions.filter(
        (
          p
        ): p is Permission & {
          module: NonNullable<Permission['module']>;
          feature: NonNullable<Permission['feature']>;
          actions: NonNullable<Permission['actions']>;
        } =>
          p.module !== undefined &&
          p.feature !== undefined &&
          p.actions !== undefined
      );

      setValue('permissions', formPermissions, {
        shouldDirty: true,
        shouldValidate: true,
      });
    },
    [setValue]
  );

  // Check if role is system role (cannot edit name)
  const isSystemRole = role?.isDefault || role?.isSystem;

  return {
    // Form methods
    control,
    handleSubmit: handleSubmit(handleFormSubmit),
    reset,
    setValue,

    // Form state
    errors,
    isSubmitting,
    isValid,
    isDirty,
    watchedValues,

    // Custom methods
    updatePermissions,

    // Role state
    isSystemRole,
    isEditing: !!role,
  };
};
