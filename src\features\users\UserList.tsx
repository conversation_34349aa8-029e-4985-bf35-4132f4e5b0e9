import React from 'react';

import LoadingSpinner from '../../components/Common/LoadingSpinner';
import { useAuth } from '../../hooks/useAuth';
import { getCurrentOrganizationId } from '../../utils/auth-utils';
import UserListComponent from './components/UserList';

const UserList: React.FC = () => {
  const {
    selectedOrganization,
    isSuperAdmin,
    isOrganizationAdmin,
    userOrganizationId,
  } = useAuth();

  // For super admins
  if (isSuperAdmin) {
    // If organization is selected from header, use the selected organization ID
    if (selectedOrganization) {
      return (
        <UserListComponent
          organizationName={selectedOrganization.name}
          organizationId={selectedOrganization.id}
        />
      );
    } else {
      // If no organization selected, show all users (don't pass organizationId)
      return (
        <UserListComponent
          organizationName='All Organizations'
          organizationId={null}
        />
      );
    }
  }

  // For organization admins, ensure we always have an organization ID
  if (isOrganizationAdmin) {
    const orgId =
      userOrganizationId ||
      selectedOrganization?.id ||
      getCurrentOrganizationId();

    if (!orgId) {
      console.error('No organization ID found for organization admin');
      return (
        <div className='p-4 text-red-600'>
          Error: Organization ID is required. Please contact support.
        </div>
      );
    }

    const organizationName = selectedOrganization?.name || 'Your Organization';

    return (
      <UserListComponent
        organizationName={organizationName}
        organizationId={orgId}
      />
    );
  }

  // Default fallback - only super admins should reach here
  // For non-super admins, we should have already returned a component with their organization ID
  console.error('Unexpected user role or missing organization ID');
  return (
    <div className='flex h-64 items-center justify-center'>
      <LoadingSpinner text='Loading users...' />
    </div>
  );
};

export default UserList;
