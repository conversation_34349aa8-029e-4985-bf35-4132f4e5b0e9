import { useSelector } from 'react-redux';

import { RootState } from '../store';
import { UserRole } from '../types';

export const useAuth = () => {
  const authState = useSelector((state: RootState) => state.auth);

  const {
    user,
    selectedOrganization,
    currentOrganization,
    isAuthenticated,
    loading,
    error,
    loggingOut,
  } = authState;

  // Check for super admin (case-insensitive check)
  const isSuperAdmin =
    user?.roles?.some(
      (role) =>
        role.name.toUpperCase() === 'SUPER_ADMIN' ||
        role.name.toLowerCase() === 'super admin' ||
        role.name.toLowerCase() === 'superadmin'
    ) ||
    localStorage.getItem('userRole')?.toUpperCase() === 'SUPER_ADMIN' ||
    localStorage.getItem('userRole')?.toLowerCase() === 'super admin' ||
    localStorage.getItem('userRole')?.toLowerCase() === 'superadmin' ||
    false;

  // Check for organization admin (case-insensitive check)
  const isOrganizationAdmin =
    user?.roles?.some(
      (role) =>
        role.name.toUpperCase() === 'ADMIN' ||
        role.name.toLowerCase() === 'admin' ||
        role.name.toLowerCase() === 'administrator' ||
        role.name.toLowerCase() === 'organization super admin' ||
        role.name.toLowerCase() === 'organization_super_admin'
    ) ||
    localStorage.getItem('userRole')?.toUpperCase() === 'ADMIN' ||
    localStorage.getItem('userRole')?.toLowerCase() === 'admin' ||
    localStorage.getItem('userRole')?.toLowerCase() === 'administrator' ||
    localStorage.getItem('userRole')?.toLowerCase() ===
      'organization super admin' ||
    localStorage.getItem('userRole')?.toLowerCase() ===
      'organization_super_admin' ||
    false;

  // Get the user's organization ID
  const userOrganizationId = user?.organizationId;

  // Check if user has access to a specific organization
  const hasOrganizationAccess = (orgId: string | undefined): boolean => {
    if (isSuperAdmin) return true; // Super admins have access to all orgs
    if (!orgId || !userOrganizationId) return false;
    return orgId === userOrganizationId; // Regular admins only have access to their org
  };

  const hasRole = (roleName: UserRole): boolean => {
    if (isSuperAdmin) return true;
    const roleNameUpper = roleName.toUpperCase();
    return (
      user?.roles.some(
        (role) =>
          role.name.toUpperCase() === roleNameUpper ||
          role.name.toLowerCase() === roleName.toLowerCase()
      ) || false
    );
  };

  const hasPermission = (
    module: string,
    feature: string,
    action: string
  ): boolean => {
    if (isSuperAdmin) return true;

    return (
      user?.roles.some((role) =>
        role.permissions.some(
          (permission) =>
            permission.module === module &&
            permission.feature === feature &&
            permission.actions?.includes(action as any)
        )
      ) || false
    );
  };

  const getDisplayName = (): string => {
    if (!user) return '';
    return `${user.firstName} ${user.lastName}`;
  };

  const getCurrentOrganization = () => {
    return selectedOrganization;
  };

  return {
    user,
    selectedOrganization,
    currentOrganization,
    isAuthenticated,
    isSuperAdmin,
    isOrganizationAdmin,
    isOrganizationUser: !isSuperAdmin && isOrganizationAdmin,
    userOrganizationId,
    loading,
    error,
    loggingOut,
    hasRole,
    hasPermission,
    hasOrganizationAccess,
    getDisplayName,
    getCurrentOrganization,
  };
};
