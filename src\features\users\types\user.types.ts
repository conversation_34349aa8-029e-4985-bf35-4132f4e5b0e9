import { Organization, Role } from '../../../types';

export interface User {
  id: string;
  firstName: string;
  lastName: string;
  name: string;
  email: string;
  phone?: string;
  roles: Role[];
  userRole: string;
  roleId?: string;
  status: 'active' | 'inactive';
  organizationId: string;
  organization?: Organization;
  lastLogin?: Date;
  createdAt: Date;
  mustResetPassword: boolean;
  organizationName?: string;
  isOrganizationMainAdmin?: boolean;
}

export interface CreateUserData {
  name: string;
  email: string;
  userRole: string;
  userType: string;
  organizationId: string;
  phone?: string;
  status?: 'active' | 'inactive';
}

export interface UpdateUserData {
  id: string;
  name?: string;
  email?: string;
  userRole?: string;
  userType?: string;
  phone?: string;
  status?: 'active' | 'inactive';
  organizationId?: string;
}

export interface UserFormData {
  name: string;
  email: string;
  userRole: string;
  status: 'active' | 'inactive';
  roleIds: string[];
}

export interface UserFilters {
  search: string;
  status: string;
  organizationId: string;
}

export interface UserPagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

export type UserRole =
  | 'SUPER_ADMIN'
  | 'ORGANIZATION_ADMIN'
  | 'DOCTOR'
  | 'NURSE'
  | 'RECEPTIONIST'
  | 'USER';

export const USER_ROLES: { value: UserRole; label: string }[] = [
  { value: 'SUPER_ADMIN', label: 'Super Admin' },
  { value: 'ORGANIZATION_ADMIN', label: 'Organization Admin' },
  { value: 'DOCTOR', label: 'Doctor' },
  { value: 'NURSE', label: 'Nurse' },
  { value: 'RECEPTIONIST', label: 'Receptionist' },
  { value: 'USER', label: 'User' },
];

export const USER_STATUSES: { value: 'active' | 'inactive'; label: string }[] =
  [
    { value: 'active', label: 'Active' },
    { value: 'inactive', label: 'Inactive' },
  ];
