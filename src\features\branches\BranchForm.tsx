import { Save, X } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';

import LoadingSpinner from '../../components/Common/LoadingSpinner';
import { useAuth } from '../../hooks/useAuth';
import { AppDispatch } from '../../store';
import { createBranch, updateBranch } from '../../store/slices/branchSlice';
import { Branch } from '../../types';

interface BranchFormProps {
  branch?: Branch | null;
  onSuccess: () => void;
}

const BranchForm: React.FC<BranchFormProps> = ({ branch, onSuccess }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { selectedOrganization } = useAuth();
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const [formData, setFormData] = useState({
    name: '',
    address: '',
    contactPhone: '',
    status: 'active' as 'active' | 'inactive',
  });

  useEffect(() => {
    if (branch) {
      setFormData({
        name: branch.name,
        address: branch.address || '',
        contactPhone: branch.contactPhone || '',
        status: branch.status,
      });
    }
  }, [branch]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    if (!formData.name.trim()) newErrors.name = 'Branch name is required';
    if (!formData.address.trim()) newErrors.address = 'Address is required';
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm() || !selectedOrganization) return;

    setLoading(true);
    try {
      const branchData = {
        ...formData,
        organizationId: selectedOrganization.id,
      };
      if (branch) {
        await dispatch(updateBranch({ id: branch.id, data: branchData }));
      } else {
        await dispatch(createBranch(branchData));
      }
      onSuccess();
    } catch {
      // Handle error silently or show user feedback
    } finally {
      setLoading(false);
    }
  };

  const handleChange =
    (field: keyof typeof formData) =>
    (
      e: React.ChangeEvent<
        HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
      >
    ) => {
      setFormData((prev) => ({ ...prev, [field]: e.target.value }));
      if (errors[field]) {
        setErrors((prev) => ({ ...prev, [field]: '' }));
      }
    };

  return (
    <form onSubmit={handleSubmit} className='p-6 space-y-6'>
      <div className='space-y-4'>
        <div>
          <label className='block text-sm font-medium text-gray-700'>
            Branch Name *
          </label>
          <input
            type='text'
            value={formData.name}
            onChange={handleChange('name')}
            className={`mt-1 block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.name ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder='Enter branch name'
          />
          {errors.name && (
            <p className='mt-1 text-sm text-red-600'>{errors.name}</p>
          )}
        </div>

        <div>
          <label className='block text-sm font-medium text-gray-700'>
            Address *
          </label>
          <input
            type='text'
            value={formData.address}
            onChange={handleChange('address')}
            className={`mt-1 block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.address ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder='Enter branch address'
          />
          {errors.address && (
            <p className='mt-1 text-sm text-red-600'>{errors.address}</p>
          )}
        </div>

        <div>
          <label className='block text-sm font-medium text-gray-700'>
            Status
          </label>
          <select
            value={formData.status}
            onChange={handleChange('status')}
            className='mt-1 block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500'
          >
            <option value='active'>Active</option>
            <option value='inactive'>Inactive</option>
          </select>
        </div>
      </div>

      <div className='flex justify-end pt-4 space-x-4'>
        <button
          type='button'
          onClick={onSuccess}
          className='inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
        >
          <X className='w-4 h-4 mr-2' />
          Cancel
        </button>
        <button
          type='submit'
          disabled={loading}
          className='inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50'
        >
          {loading ? <LoadingSpinner /> : <Save className='w-4 h-4 mr-2' />}
          {branch ? 'Save Changes' : 'Create Branch'}
        </button>
      </div>
    </form>
  );
};

export default BranchForm;
