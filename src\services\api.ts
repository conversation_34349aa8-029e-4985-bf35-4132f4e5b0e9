import axios, { CancelTokenSource } from 'axios';

import { API_ENDPOINT, SUBSCRIPTION_KEY } from '../constants/api-endpoints';
import { getToken } from '../utils/authUtils';

// Custom error class for cancelled requests
class RequestCancelledError extends Error {
  constructor() {
    super('Request was cancelled');
    this.name = 'RequestCancelledError';
  }
}

declare module 'axios' {
  export interface AxiosRequestConfig {
    cancelDuplicateRequest?: boolean;
    _isCancelled?: boolean;
  }
}

// Map to store pending requests
const pendingRequests = new Map<string, CancelTokenSource>();

/**
 * Generate a unique key for each request based on method and URL
 */
const generateRequestKey = (config: any): string => {
  const { method, url, params, data } = config;
  // For POST/PUT requests, include a hash of the request data to differentiate between different payloads
  const dataKey = ['POST', 'PUT', 'PATCH'].includes(method?.toUpperCase())
    ? JSON.stringify(data)
    : '';
  return `${method?.toLowerCase()}:${url}?${new URLSearchParams(params).toString()}:${dataKey}`;
};

/**
 * Cancel duplicate pending requests
 */
const cancelPendingRequest = (key: string) => {
  if (pendingRequests.has(key)) {
    const source = pendingRequests.get(key);
    // Mark the request as cancelled before cancelling
    if (source?.token) {
      (source.token as any)._isCancelled = true;
    }
    source?.cancel();
    pendingRequests.delete(key);
  }
};

const api = axios.create({
  baseURL: API_ENDPOINT,
  headers: {
    'Content-Type': 'application/json',
    'Ocp-Apim-Subscription-Key': SUBSCRIPTION_KEY,
  },
  timeout: 30000, // 30 seconds timeout
});

// Request interceptor to add auth token and handle token refresh
api.interceptors.request.use(
  async (config) => {
    // Skip token check for login/refresh token endpoints
    if (config.url?.includes('/auth/') || config.url?.includes('/login')) {
      return config;
    }

    // Generate a unique key for the request
    const requestKey = generateRequestKey(config);

    // If this is a duplicate request, cancel the previous one
    if (config.cancelDuplicateRequest !== false) {
      cancelPendingRequest(requestKey);

      // Create a new cancel token for this request
      const source = axios.CancelToken.source();
      config.cancelToken = source.token;

      // Store the cancel token
      pendingRequests.set(requestKey, source);
    }

    try {
      // Get a fresh token for each request
      const token = await getToken();

      if (token) {
        // Ensure the token is properly formatted
        const formattedToken = token.startsWith('Bearer ')
          ? token
          : `Bearer ${token}`;
        config.headers.Authorization = formattedToken;
      } else {
        console.warn('No token available for request:', config.url);
      }

      return config;
    } catch (error) {
      console.error('Error setting auth token:', error);
      // Clear auth state and redirect to login on token error
      localStorage.removeItem('token');
      sessionStorage.removeItem('msal.accessToken');
      window.location.href = '/login';
      return Promise.reject(error);
    }
  },
  (error) => {
    console.error('Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Track if we're already trying to refresh the token
let isRefreshing = false;
// Store the failed requests to retry them later
let failedQueue: Array<{
  resolve: (token: string) => void;
  reject: (error: any) => void;
}> = [];

const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach((prom) => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token || '');
    }
  });
  failedQueue = [];
};

// Response interceptor to handle responses and clean up pending requests
api.interceptors.response.use(
  (response) => {
    // Clean up the pending request
    const requestKey = generateRequestKey(response.config);
    pendingRequests.delete(requestKey);
    return response;
  },
  async (error) => {
    // Check if this is a cancelled request
    if (axios.isCancel(error) || error?.config?._isCancelled) {
      // Return a special error that won't trigger toast
      return Promise.reject(new RequestCancelledError());
    }

    const originalRequest = error.config;

    // Clean up the pending request on error
    if (originalRequest) {
      const requestKey = generateRequestKey(originalRequest);
      pendingRequests.delete(requestKey);
    }

    // Log the error for debugging (except for cancelled requests)
    if (!axios.isCancel(error)) {
      console.error('API Error:', {
        url: originalRequest?.url,
        status: error.response?.status,
        message: error.message,
        response: error.response?.data,
      });
    }

    // Handle 401 Unauthorized
    if (error.response?.status === 401 && !originalRequest._retry) {
      // If we're already refreshing the token, add the request to the queue
      if (isRefreshing) {
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        })
          .then((token) => {
            originalRequest.headers.Authorization = `Bearer ${token}`;
            return api(originalRequest);
          })
          .catch((err) => {
            return Promise.reject(err);
          });
      }

      // Mark that we're refreshing the token
      isRefreshing = true;
      originalRequest._retry = true;

      try {
        // Clear any existing tokens to force a refresh
        localStorage.removeItem('token');
        sessionStorage.removeItem('msal.accessToken');

        // Get a new token
        const newToken = await getToken();

        if (newToken) {
          // Update the authorization header
          originalRequest.headers.Authorization = `Bearer ${newToken}`;

          // Process the queue with the new token
          processQueue(null, newToken);

          // Retry the original request
          return api(originalRequest);
        } else {
          throw new Error('Failed to refresh token');
        }
      } catch (refreshError) {
        console.error('Token refresh failed:', refreshError);

        // Process the queue with the error
        processQueue(refreshError, null);

        // Clear all auth-related data
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        sessionStorage.removeItem('msal.accessToken');
        sessionStorage.removeItem('msal.idToken');

        // Don't redirect if we're already on the login page
        if (!window.location.pathname.includes('/login')) {
          // Store the current URL to redirect back after login
          sessionStorage.setItem('preLoginUrl', window.location.pathname);
          window.location.href = '/login';
        }

        return Promise.reject(refreshError);
      } finally {
        isRefreshing = false;
      }
    }

    return Promise.reject(error);
  }
);

export default api;
