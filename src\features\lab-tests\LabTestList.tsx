import React from 'react';

import LoadingSpinner from '../../components/Common/LoadingSpinner';
import OrganizationRequired from '../../components/Common/OrganizationRequired';
import { useAuth } from '../../hooks/useAuth';
import { getCurrentOrganizationId } from '../../utils/auth-utils';
import LabTestListComponent from './components/LabTestList';

const LabTestList: React.FC = () => {
  const {
    selectedOrganization,
    isSuperAdmin,
    isOrganizationAdmin,
    userOrganizationId,
  } = useAuth();

  // For super admins, require organization selection
  if (isSuperAdmin) {
    // If organization is selected from header, use the selected organization ID
    if (selectedOrganization) {
      return (
        <LabTestListComponent
          organizationName={selectedOrganization.name}
          organizationId={selectedOrganization.id}
        />
      );
    } else {
      // If no organization selected, show organization selection required
      return (
        <OrganizationRequired
          feature='lab tests'
          customMessage='Please select an organization to view lab tests.'
          customTitle='Organization Required'
        />
      );
    }
  }

  // For organization admins, use the organizationId from the user's auth info
  if (isOrganizationAdmin) {
    const orgId =
      userOrganizationId ||
      selectedOrganization?.id ||
      getCurrentOrganizationId();

    if (orgId) {
      const organizationName =
        selectedOrganization?.name || 'Your Organization';

      return (
        <LabTestListComponent
          organizationName={organizationName}
          organizationId={orgId}
        />
      );
    } else {
      // If no organizationId, show error message
      console.error('No organization ID found for organization admin');
      return (
        <div className='p-4 text-center text-red-500'>
          Organization not found. Please contact support.
        </div>
      );
    }
  }

  // Show loading state while checking permissions
  return (
    <div className='flex h-64 items-center justify-center'>
      <LoadingSpinner text='Loading lab tests...' />
    </div>
  );
};

export default LabTestList;
