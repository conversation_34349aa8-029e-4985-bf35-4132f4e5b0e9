import { toast } from 'react-toastify';

import authService from '../store/features/auth/auth.service';
import { getAccessToken, logout as msalLogout, msalInstance } from './msal';
import { apiScopes } from './msalConfig';

export async function login() {
  try {
    await msalInstance.loginRedirect({
      scopes: [apiScopes.apiRead],
    });
  } catch (error) {
    console.error('Login error:', error);
    toast.error('Login failed. Please try again.');
  }
}

export async function logout() {
  try {
    await msalLogout();
    localStorage?.clear();
  } catch (error) {
    console.error('Logout error:', error);
    toast.error('Logout failed');
  }
}

export async function getToken(): Promise<string> {
  try {
    const token = await getAccessToken();

    if (!token) {
      console.error('getToken: No token received from getAccessToken');
      throw new Error('Failed to get access token: No token received');
    }

    return token;
  } catch (error) {
    console.error('getToken: Error getting access token:', {
      error,
      message: error instanceof Error ? error.message : 'Unknown error',
    });

    // Don't redirect here, let the caller handle the error
    throw error;
  }
}

export function getActiveAccount() {
  try {
    return msalInstance.getActiveAccount();
  } catch (error) {
    console.error('Error getting active account:', error);
    return null;
  }
}

// This function is now a wrapper around the auth service's fetchEmrUserInfo
export async function fetchUserInfo() {
  try {
    // Get the active account from MSAL
    const account = getActiveAccount();
    if (!account?.username) {
      throw new Error('No active account found');
    }

    // Use the auth service to fetch EMR user info
    const { user, token } = await authService.fetchEmrUserInfo(
      account.username
    );

    return {
      token,
      user,
    };
  } catch (error) {
    console.error('Error in fetchUserInfo:', error);

    // Clear token on error to force re-authentication
    localStorage.removeItem('token');
    sessionStorage.removeItem('msal.accessToken');

    throw error;
  }
}
