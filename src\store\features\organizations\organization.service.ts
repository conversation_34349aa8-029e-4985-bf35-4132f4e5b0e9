import { ORGANIZATIONS_ENDPOINT } from '../../../constants/api-endpoints';
import api from '../../../services/api';

const apiUrl = ORGANIZATIONS_ENDPOINT;
const pagination = { size: 10 };

export type OrganizationParams = {
  name?: string; // Changed from search to name to match API
  status?: string;
  page?: number;
  pageSize?: number; // Changed from size to pageSize to match API
};

export type CreateOrganizationData = {
  name: string;
  contactPersonName: string;
  contactEmail: string;
  contactPhone?: string;
  address: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  description?: string;
};

export type UpdateOrganizationData = {
  id: string;
  name?: string;
  contactPersonName?: string;
  contactEmail?: string;
  contactPhone?: string;
  address?: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  description?: string;
};

const fetchOrganizations = async ({
  name = '',
  status = '',
  page = 1,
  pageSize = pagination.size,
}: OrganizationParams = {}) => {
  // Build query parameters for the API
  const queryParams: Record<string, string | number> = {
    page,
    pageSize,
  };

  // Add name filter if provided
  if (name && name.trim()) {
    queryParams.name = name.trim();
  }

  // Add status filter if provided
  if (status) {
    queryParams.status = status.trim();
  }

  const response = await api.get(`${apiUrl}/list-organizations`, {
    params: queryParams,
  });

  // Handle the actual API response structure
  // API returns { records: Organization[], totalRecords: number, totalPages: number, currentPage: number }
  return {
    data: response.data.records || [],
    total: response.data.totalRecords || 0,
    page: response.data.currentPage || page,
    pageSize,
    totalPages: response.data.totalPages || 0,
  };
};

const fetchOrganizationById = async (id: string) => {
  const response = await api.get(`${apiUrl}/organization?organizationId=${id}`);
  // Return the organization data directly from response.data
  // The API response structure has the organization object directly in data
  const orgData = response.data;

  // Transform the data to match our Organization type
  return {
    ...orgData,
    status: orgData.isActive ? 'active' : 'inactive', // Add status field based on isActive
  };
};

const createOrganization = async (data: CreateOrganizationData) => {
  const response = await api.post(`${apiUrl}/organization`, data);
  return response.data.data;
};

const updateOrganization = async (data: UpdateOrganizationData) => {
  const response = await api.patch(`${apiUrl}/organization`, data);
  return response.data.data;
};

const deleteOrganization = async (id: string) => {
  const response = await api.delete(`${apiUrl}/organization`, {
    params: { organizationId: id },
  });
  return response.data;
};

const toggleOrganizationStatus = async (id: string) => {
  const response = await api.patch(`${apiUrl}/toggle-status`, { id });
  return response.data.data;
};

const fetchAllOrganizations = async () => {
  // Fetch all active organizations for dropdown (no pagination)
  const response = await api.get(`${apiUrl}/list-organizations`, {
    params: {
      pageSize: 1000, // Large number to get all organizations
      page: 1,
    },
  });

  return {
    data: response.data.records || [],
    total: response.data.totalRecords || 0,
  };
};

const organizationService = {
  fetchOrganizations,
  fetchAllOrganizations,
  fetchOrganizationById,
  createOrganization,
  updateOrganization,
  deleteOrganization,
  toggleOrganizationStatus,
};

export default organizationService;
