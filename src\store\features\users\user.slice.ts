import {
  createAsyncThunk,
  createSlice,
  isPending,
  isRejected,
} from '@reduxjs/toolkit';

import { User } from '../../../types';
import { ApiError, InitialState } from '../../../utils/reducer-utils';
import userService, {
  CreateUserData,
  UpdateUserData,
  UserParams,
} from './user.service';

interface ApiUser {
  id: string;
  name?: string;
  email?: string;
  userRole?: string;
  role?: string;
  userType?: string;
  isActive?: boolean;
  organizationId?: string;
  roleId?: string;
  resetToken?: string | null;
  resetTokenExpiry?: string | null;
  password?: string;
  organizationName?: string;
  isOrganizationMainAdmin?: boolean;
}

type ExtendedInitialState = InitialState<User> & {
  users: User[];
  currentUser: User | null;
  total: number;
  totalPages: number;
  page: number;
  limit: number;
  searchName: string;
  statusFilter: string;
  error: string | null;
};

const initialState: ExtendedInitialState = {
  loading: false,
  updating: false,
  updateSuccess: false,
  entity: null,
  entities: [],
  errorMessage: null,
  successMessage: null,
  allEntities: [],
  users: [],
  currentUser: null,
  total: 0,
  totalPages: 0,
  page: 1,
  limit: 10,
  searchName: '',
  statusFilter: 'all', // Matches statusFilterOptions "All Status" value
  error: null,
};

export const fetchUsers = createAsyncThunk(
  'users/fetchUsers',
  async (params: UserParams, { rejectWithValue }) => {
    try {
      return await userService.fetchUsers(params);
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const fetchUserByEmail = createAsyncThunk(
  'users/fetchUserByEmail',
  async (email: string, { rejectWithValue }) => {
    try {
      return await userService.fetchUserByEmail(email);
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const createUser = createAsyncThunk(
  'users/createUser',
  async (data: CreateUserData, { rejectWithValue, dispatch, getState }) => {
    try {
      const state = getState() as { auth: { user: User } };
      const currentUser = state.auth.user;

      // For organization admins, always use their organization ID
      if (
        currentUser &&
        currentUser.userRole !== 'SUPER_ADMIN' &&
        currentUser.organizationId
      ) {
        data.organizationId = currentUser.organizationId;
      }

      const response = await userService.createUser(data);

      // Prepare fetch params for refetching users
      const fetchParams: UserParams = {
        page: 1,
        pageSize: 10,
        search: '',
        isActive: true,
      };

      // Include organization ID in fetch params for organization admins
      if (
        currentUser &&
        currentUser.userRole !== 'SUPER_ADMIN' &&
        currentUser.organizationId
      ) {
        fetchParams.organizationId = currentUser.organizationId;
      } else if (data.organizationId) {
        // For super admins, use the selected organization ID if provided
        fetchParams.organizationId = data.organizationId;
      }

      await dispatch(fetchUsers(fetchParams));
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const updateUser = createAsyncThunk(
  'users/updateUser',
  async (data: UpdateUserData, { rejectWithValue, dispatch, getState }) => {
    try {
      const state = getState() as {
        users: ExtendedInitialState;
        auth: { user: User };
      };

      const currentUser = state.auth.user;

      // For organization admins, ensure they can only update users in their organization
      if (
        currentUser &&
        currentUser.userRole !== 'SUPER_ADMIN' &&
        currentUser.organizationId
      ) {
        data.organizationId = currentUser.organizationId;
      }

      const response = await userService.updateUser(data);

      // Prepare fetch params for refetching users
      const fetchParams: UserParams = {
        page: state.users.page,
        pageSize: state.users.limit,
        search: state.users.searchName,
      };

      // Add status filter if a specific status is selected
      if (state.users.statusFilter === 'active') {
        fetchParams.isActive = true;
      } else if (state.users.statusFilter === 'inactive') {
        fetchParams.isActive = false;
      }

      // Include organization ID in fetch params for organization admins
      if (
        currentUser &&
        currentUser.userRole !== 'SUPER_ADMIN' &&
        currentUser.organizationId
      ) {
        fetchParams.organizationId = currentUser.organizationId;
      } else if (data.organizationId) {
        // For super admins, use the selected organization ID if provided
        fetchParams.organizationId = data.organizationId;
      }

      await dispatch(fetchUsers(fetchParams));
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const deleteUser = createAsyncThunk(
  'users/deleteUser',
  async (id: string, { rejectWithValue }) => {
    try {
      await userService.deleteUser(id);
      return id;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const resetUserPassword = createAsyncThunk(
  'users/resetUserPassword',
  async (id: string, { rejectWithValue }) => {
    try {
      return await userService.resetUserPassword(id);
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

const userSlice = createSlice({
  name: 'users',
  initialState,
  reducers: {
    setSearchName: (state, action) => {
      state.searchName = action.payload;
      state.page = 1;
    },
    setStatusFilter: (state, action) => {
      state.statusFilter = action.payload;
      state.page = 1;
    },
    setPage: (state, action) => {
      state.page = action.payload;
    },
    setPageSize: (state, action) => {
      state.limit = action.payload;
      state.page = 1;
    },
    resetFilters: (state) => {
      state.searchName = '';
      state.statusFilter = 'all'; // Reset to "All Status" option
      state.page = 1;
    },
    clearMessages: (state) => {
      state.errorMessage = null;
      state.successMessage = null;
    },
    clearUpdateSuccess: (state) => {
      state.updateSuccess = false;
      state.successMessage = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchUsers.fulfilled, (state, action) => {
        // Transform API response to match our User interface
        state.users = (action?.payload?.data || []).map((user: ApiUser) => ({
          id: user.id || '',
          firstName: user.name?.split(' ')[0] || '',
          lastName: user.name?.split(' ').slice(1).join(' ') || '',
          name: user.name || '',
          email: user.email || '',
          userRole: user.role || user.userRole || 'USER',
          status: user.isActive ? 'active' : 'inactive',
          organizationId: user.organizationId || '',
          roles: [], // Default empty array for roles
          createdAt: new Date(), // Default to current date
          mustResetPassword: false, // Default to false
          organizationName: user.organizationName ?? '',
          isOrganizationMainAdmin: user.isOrganizationMainAdmin ?? false,
        }));

        state.total = action?.payload?.total || 0;
        state.totalPages = action?.payload?.totalPages || 0;
        state.page = action?.payload?.page || state.page;
        state.limit = action?.payload?.pageSize || state.limit;
        state.loading = false;
        state.error = null;
      })
      .addCase(fetchUserByEmail.fulfilled, (state, action) => {
        state.currentUser = action.payload;
        state.loading = false;
        state.error = null;
      })
      .addCase(createUser.fulfilled, (state) => {
        state.updating = false;
        state.updateSuccess = true;
        state.successMessage =
          'User created successfully! A confirmation email has been sent to the registered email address.';
        state.errorMessage = null;
      })
      .addCase(createUser.rejected, (state, action) => {
        state.updating = false;
        state.updateSuccess = false;
        const error = action.payload as ApiError;
        state.error =
          (typeof error?.response?.data === 'string'
            ? error.response.data
            : error?.response?.data?.message) ||
          error?.message ||
          'Failed to create user. Please try again.';
        state.successMessage = null;
      })
      .addCase(updateUser.fulfilled, (state) => {
        state.updating = false;
        state.updateSuccess = true;
        state.successMessage = 'User updated successfully';
        state.errorMessage = null;
      })
      .addCase(updateUser.rejected, (state, action) => {
        state.updating = false;
        state.updateSuccess = false;
        const error = action.payload as ApiError;
        state.errorMessage =
          error?.response?.data?.message ||
          error?.message ||
          'Failed to update user. Please try again.';
        state.successMessage = null;
      })
      .addCase(deleteUser.fulfilled, (state, action) => {
        state.updating = false;
        state.updateSuccess = true;
        state.successMessage = 'User deleted successfully';
        state.errorMessage = null;

        // Remove the user from the list
        const deletedUserId = action.meta.arg;
        if (deletedUserId) {
          state.users = state.users.filter((user) => user.id !== deletedUserId);
          state.total = Math.max(0, state.total - 1);
        }
      })
      .addCase(deleteUser.rejected, (state, action) => {
        state.updating = false;
        state.updateSuccess = false;
        const error = action.payload as ApiError;
        state.errorMessage =
          error?.response?.data?.message ||
          error?.message ||
          'Failed to delete user. Please try again.';
        state.successMessage = null;
      })
      .addCase(resetUserPassword.fulfilled, (state) => {
        state.updating = false;
        state.updateSuccess = true;
        state.successMessage = 'Password reset successfully';
        state.errorMessage = null;
      })
      .addCase(resetUserPassword.rejected, (state, action) => {
        state.updating = false;
        state.updateSuccess = false;
        const error = action.payload as ApiError;
        state.errorMessage =
          error?.response?.data?.message ||
          error?.message ||
          'Failed to reset password. Please try again.';
        state.successMessage = null;
      })
      .addMatcher(isPending(fetchUsers, fetchUserByEmail), (state) => {
        state.loading = true;
        state.error = null;
      })
      .addMatcher(
        isPending(createUser, updateUser, deleteUser, resetUserPassword),
        (state) => {
          state.updating = true;
          state.updateSuccess = false;
          state.errorMessage = null;
          state.successMessage = null;
        }
      )
      .addMatcher(isRejected(fetchUsers, fetchUserByEmail), (state, action) => {
        state.loading = false;
        const error = action.payload as ApiError;
        state.error =
          error?.response?.data?.message ||
          error?.message ||
          'Failed to load users. Please try again.';
      })
      // Reset slice on logout
      .addMatcher(
        (action) => action.type === 'RESET_ALL_SLICES',
        () => initialState
      );
  },
});

// Export actions
export const {
  setSearchName,
  setStatusFilter,
  setPage,
  setPageSize,
  resetFilters,
  clearMessages,
  clearUpdateSuccess,
} = userSlice.actions;

export default userSlice.reducer;
