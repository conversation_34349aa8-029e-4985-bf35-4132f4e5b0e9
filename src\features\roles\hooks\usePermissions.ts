import { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { AppDispatch, RootState } from '../../../store';
import {
  assignPermissions,
  clearAssignMessages,
  clearRolePermissions,
  getRolePermissions,
} from '../../../store/features/permissions/permission.slice';

export const usePermissions = () => {
  const dispatch = useDispatch<AppDispatch>();

  const {
    rolePermissions,
    allPermissions,
    loading,
    assignLoading,
    assignError,
    assignSuccess,
    error,
  } = useSelector((state: RootState) => state.permissions);

  // Fetch permissions for a specific role
  const fetchRolePermissions = useCallback(
    (roleId: string) => {
      dispatch(getRolePermissions({ roleId }));
    },
    [dispatch]
  );

  // Assign permissions to a role
  const handleAssignPermissions = useCallback(
    async (roleId: string, permissionIds: string[]) => {
      const result = await dispatch(
        assignPermissions({
          roleId,
          permissions: permissionIds,
        })
      );
      return result;
    },
    [dispatch]
  );

  // Update permissions for a role (PATCH method)
  const handleUpdatePermissions = useCallback(
    async (roleId: string, permissionIds: string[]) => {
      // Import updatePermissions dynamically to avoid circular dependency
      const { updatePermissions } = await import(
        '../../../store/features/permissions/permission.slice'
      );
      const result = await dispatch(
        updatePermissions({
          roleId,
          permissions: permissionIds,
        })
      );
      return result;
    },
    [dispatch]
  );

  // Clear role permissions
  const clearRolePermissionsData = useCallback(() => {
    dispatch(clearRolePermissions());
  }, [dispatch]);

  // Clear assignment messages
  const clearMessages = useCallback(() => {
    dispatch(clearAssignMessages());
  }, [dispatch]);

  return {
    // Data
    rolePermissions,
    allPermissions,

    // Loading states
    loading,
    assignLoading,

    // Error states
    error,
    assignError,
    assignSuccess,

    // Actions
    fetchRolePermissions,
    assignPermissions: handleAssignPermissions,
    updatePermissions: handleUpdatePermissions,
    clearRolePermissions: clearRolePermissionsData,
    clearMessages,
  };
};
