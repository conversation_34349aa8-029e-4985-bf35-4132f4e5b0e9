import { USERS_ENDPOINT } from '../../../constants/api-endpoints';
import api from '../../../services/api';
import {
  getSelectedOrganization,
  isSuperAdmin,
} from '../../../utils/auth-utils';

const apiUrl = USERS_ENDPOINT;
const pagination = { size: 10 };

export type UserParams = {
  organizationId?: string | null; // Optional for super admin without organization selection (null means no filter)
  search?: string; // For search functionality
  page?: number;
  pageSize?: number;
  isActive?: boolean; // Status filter
};

export type CreateUserData = {
  name: string;
  email: string;
  userRole: string;
  userType: string;
  organizationId: string;
  roleId: string;
  phone?: string;
};

export type UpdateUserData = {
  id: string;
  name?: string;
  email?: string;
  userRole?: string;
  userType?: string;
  organizationId?: string;
  roleId?: string;
  status?: boolean;
  phone?: string;
};

export type UserResponse = {
  id: string;
  name: string;
  email: string;
  userRole: string;
  userType: string;
  isActive: boolean;
  organizationId: string;
  roleId?: string;
  resetToken?: string | null;
  resetTokenExpiry?: string | null;
  created_by: string;
  updated_by: string;
  created_on: string;
  updated_on: string;
  password?: string;
  _rid?: string;
  _self?: string;
  _etag?: string;
  _attachments?: string;
  _ts?: number;
};

export type UsersListResponse = {
  items: UserResponse[];
  totalItemCount: number;
  totalPages: number;
  currentPage: number;
};

const fetchUsers = async (params: UserParams) => {
  const {
    organizationId,
    search = '',
    page = 1,
    pageSize = pagination.size,
    isActive,
  } = params;

  // Get selected organization
  const selectedOrg = getSelectedOrganization();

  // Build query parameters for the API
  const queryParams: Record<string, string | number | boolean> = {
    page,
    pageSize,
  };

  // Add search parameter if provided
  if (search) {
    queryParams.search = search;
  }

  // Add isActive filter if provided
  if (isActive !== undefined) {
    queryParams.isActive = isActive;
  }

  // Determine if we should use the organization ID
  const shouldUseOrgId = !isSuperAdmin() || (isSuperAdmin() && selectedOrg?.id);

  // Add organizationId to query if:
  // 1. User is not a super admin (always filter by their org)
  // 2. User is a super admin AND has an organization selected
  if (shouldUseOrgId) {
    // Use the explicitly passed organizationId if provided, otherwise fall back to selected org
    const orgIdToUse =
      organizationId !== undefined ? organizationId : selectedOrg?.id;
    if (orgIdToUse) {
      queryParams.organizationId = orgIdToUse;
    }
  }

  const response = await api.get<UsersListResponse>(`${apiUrl}/user/list`, {
    params: queryParams,
  });

  // Transform the response to match our expected format
  return {
    data: response.data.items || [],
    total: response.data.totalItemCount || 0,
    totalPages: response.data.totalPages || 1,
    page: response.data.currentPage || page,
    pageSize,
  };
};

const fetchUserByEmail = async (email: string) => {
  const response = await api.get(`${apiUrl}/user`, {
    params: { email },
  });

  return response.data.user;
};

const createUser = async (data: CreateUserData) => {
  const response = await api.post(`${apiUrl}/user`, data);
  return response.data;
};

const updateUser = async (data: UpdateUserData) => {
  const { id, ...updateData } = data;

  // Prepare the payload with only the required fields
  const payload = {
    name: updateData.name,
    userRole: updateData.userRole,
    userType: updateData.userType || updateData.userRole, // Include userType, default to userRole if not provided
    roleId: updateData.roleId,
    isActive: updateData.status, // status is already a boolean in UpdateUserData
  };

  // Remove undefined values from the payload
  const cleanPayload = Object.fromEntries(
    Object.entries(payload).filter(([_, v]) => v !== undefined)
  );

  // Use query parameter for userId and simplified payload
  const response = await api.patch(`${apiUrl}/user?userId=${id}`, cleanPayload);

  return response.data;
};

const deleteUser = async (userId: string) => {
  const response = await api.delete(`${apiUrl}/user`, {
    params: { userId },
  });
  return response.data;
};

const resetUserPassword = async (id: string) => {
  const response = await api.post(`${apiUrl}/user/reset-password`, { id });
  return response.data;
};

const userService = {
  fetchUsers,
  fetchUserByEmail,
  createUser,
  updateUser,
  deleteUser,
  resetUserPassword,
};

export default userService;
