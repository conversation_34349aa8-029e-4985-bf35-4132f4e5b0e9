import {
  createAsyncThunk,
  createSlice,
  isPending,
  isRejected,
} from '@reduxjs/toolkit';

import { Permission } from '../../../types';
import { ApiError } from '../../../utils/reducer-utils';
import permissionService, {
  AssignPermissionsData,
  GetRolePermissionsParams,
} from './permission.service';

interface PermissionState {
  rolePermissions: Permission[];
  allPermissions: Permission[];
  loading: boolean;
  error: string | null;
  assignLoading: boolean;
  assignError: string | null;
  assignSuccess: boolean;
  successMessage: string | null;
}

const initialState: PermissionState = {
  rolePermissions: [],
  allPermissions: [],
  loading: false,
  error: null,
  assignLoading: false,
  successMessage: null,
  assignError: null,
  assignSuccess: false,
};

// Async thunks
export const assignPermissions = createAsyncThunk(
  'permissions/assignPermissions',
  async (data: AssignPermissionsData, { rejectWithValue }) => {
    try {
      const response = await permissionService.assignPermissions(data);
      return { ...response, roleId: data.roleId };
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const updatePermissions = createAsyncThunk(
  'permissions/updatePermissions',
  async (data: AssignPermissionsData, { rejectWithValue }) => {
    try {
      const response = await permissionService.updatePermissions(data);
      return { ...response, roleId: data.roleId };
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const getRolePermissions = createAsyncThunk(
  'permissions/getRolePermissions',
  async (params: GetRolePermissionsParams, { rejectWithValue }) => {
    try {
      return await permissionService.getRolePermissions(params);
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

const permissionSlice = createSlice({
  name: 'permissions',
  initialState,
  reducers: {
    clearAssignMessages: (state) => {
      state.assignSuccess = false;
      state.assignError = null;
      state.successMessage = null;
    },
    clearRolePermissions: (state) => {
      state.rolePermissions = [];
    },
  },
  extraReducers: (builder) => {
    // Assign permissions
    builder.addCase(assignPermissions.fulfilled, (state, _action) => {
      state.assignLoading = false;
      state.assignSuccess = true;
      state.assignError = null;
      state.successMessage = 'Permissions assigned successfully';
    });

    // Update permissions
    builder.addCase(updatePermissions.fulfilled, (state, _action) => {
      state.assignLoading = false;
      state.assignSuccess = true;
      state.assignError = null;
      state.successMessage = 'Permissions updated successfully';
    });

    // Get role permissions
    builder.addCase(getRolePermissions.fulfilled, (state, action) => {
      state.loading = false;
      // Transform API response to match our permission format
      // Filter out permissions with null keys and map to our format
      state.rolePermissions = (action.payload.data || [])
        .filter(
          (permission: {
            key: string | null;
            api: string;
            methods: string[];
          }) => permission.key !== null && permission.key !== undefined
        )
        .map((permission: { key: string; api: string; methods: string[] }) => ({
          id: permission.key,
          key: permission.key,
          api: permission.api,
          methods: permission.methods,
        }));
      state.error = null;
    });

    // Handle pending states
    builder.addMatcher(isPending, (state, action) => {
      if (
        action.type.includes('assignPermissions') ||
        action.type.includes('updatePermissions')
      ) {
        state.assignLoading = true;
        state.assignError = null;
      } else {
        state.loading = true;
        state.error = null;
      }
    });

    // Handle rejected states
    builder.addMatcher(isRejected, (state, action) => {
      const error = action.payload as ApiError;
      if (
        action.type.includes('assignPermissions') ||
        action.type.includes('updatePermissions')
      ) {
        state.assignLoading = false;
        state.error =
          error?.response?.data?.message ||
          error?.message ||
          'Failed to assign permissions. Please try again.';
      } else {
        state.loading = false;
        state.error =
          (typeof error?.response?.data === 'string'
            ? error.response.data
            : error?.response?.data?.message) ||
          error?.message ||
          'Failed to load permissions. Please try again.';
      }
    });
  },
});

export const { clearAssignMessages, clearRolePermissions } =
  permissionSlice.actions;

export default permissionSlice.reducer;
