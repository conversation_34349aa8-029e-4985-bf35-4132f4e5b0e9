import { lazy, Suspense } from 'react';
import { Route, Routes } from 'react-router-dom';

import LoadingSpinner from './components/Common/LoadingSpinner';
import NotFoundPage from './components/Common/NotFoundPage';
import ProtectedRoute from './components/Common/ProtectedRoute';
import UnauthorizedPage from './components/Common/UnauthorizedPage';
import MainLayout from './components/Layout/MainLayout';
import { PATHS } from './constants/paths';
import LandingPage from './pages/LandingPage';

// Lazy load main feature components
const Dashboard = lazy(() => import('./features/dashboard/Dashboard'));
const UserList = lazy(() => import('./features/users/UserList'));
const RoleList = lazy(() => import('./features/roles/RoleList'));
const OrganizationList = lazy(
  () => import('./features/organizations/OrganizationList')
);
const DepartmentList = lazy(
  () => import('./features/departments/DepartmentList')
);
const BranchList = lazy(() => import('./features/branches/BranchList'));
const BankList = lazy(() => import('./features/banks/BankList'));
const LanguageList = lazy(() => import('./features/languages/LanguageList'));
const LabTestList = lazy(() => import('./features/lab-tests/LabTestList'));
const TemplateList = lazy(() => import('./features/templates/TemplateList'));
const VitalList = lazy(() => import('./features/vitals/VitalList'));
const MedicineList = lazy(() => import('./features/medicines/MedicineList'));
const PatientList = lazy(() => import('./features/patients/PatientList'));

// Loading fallback component
const LazyLoadingFallback = () => (
  <div className='flex items-center justify-center min-h-[200px]'>
    <LoadingSpinner size='md' text='Loading...' />
  </div>
);

const RoutesComponent = () => {
  return (
    <Routes>
      {/* Public landing page - shown at both root and /login */}
      <Route path={PATHS.ROOT} element={<LandingPage />} />
      <Route path={PATHS.LOGIN} element={<LandingPage />} />

      {/* Protected routes */}
      <Route element={<ProtectedRoute />}>
        <Route element={<MainLayout />}>
          <Route
            path={PATHS.DASHBOARD}
            element={
              <Suspense fallback={<LazyLoadingFallback />}>
                <Dashboard />
              </Suspense>
            }
          />
          <Route
            path={PATHS.LANDING}
            element={
              <Suspense fallback={<LazyLoadingFallback />}>
                <LandingPage />
              </Suspense>
            }
          />
          <Route
            path={PATHS.DASHBOARD}
            element={
              <Suspense fallback={<LazyLoadingFallback />}>
                <Dashboard />
              </Suspense>
            }
          />
          <Route
            path={PATHS.USERS}
            element={
              <Suspense fallback={<LazyLoadingFallback />}>
                <UserList />
              </Suspense>
            }
          />
          <Route
            path={PATHS.ROLES}
            element={
              <Suspense fallback={<LazyLoadingFallback />}>
                <RoleList />
              </Suspense>
            }
          />
          <Route
            path={PATHS.ORGANIZATIONS}
            element={
              <Suspense fallback={<LazyLoadingFallback />}>
                <OrganizationList />
              </Suspense>
            }
          />
          <Route
            path={PATHS.DEPARTMENTS}
            element={
              <Suspense fallback={<LazyLoadingFallback />}>
                <DepartmentList />
              </Suspense>
            }
          />
          <Route
            path={PATHS.BRANCHES}
            element={
              <Suspense fallback={<LazyLoadingFallback />}>
                <BranchList />
              </Suspense>
            }
          />
          <Route
            path={PATHS.BANKS}
            element={
              <Suspense fallback={<LazyLoadingFallback />}>
                <BankList />
              </Suspense>
            }
          />
          <Route
            path={PATHS.LANGUAGES}
            element={
              <Suspense fallback={<LazyLoadingFallback />}>
                <LanguageList />
              </Suspense>
            }
          />
          <Route
            path={PATHS.LAB_TESTS}
            element={
              <Suspense fallback={<LazyLoadingFallback />}>
                <LabTestList />
              </Suspense>
            }
          />
          <Route
            path={PATHS.TEMPLATES}
            element={
              <Suspense fallback={<LazyLoadingFallback />}>
                <TemplateList />
              </Suspense>
            }
          />
          <Route
            path={PATHS.VITALS}
            element={
              <Suspense fallback={<LazyLoadingFallback />}>
                <VitalList />
              </Suspense>
            }
          />
          <Route
            path={PATHS.MEDICINES}
            element={
              <Suspense fallback={<LazyLoadingFallback />}>
                <MedicineList />{' '}
              </Suspense>
            }
          />
          <Route
            path={PATHS.PATIENTS}
            element={
              <Suspense fallback={<LazyLoadingFallback />}>
                <PatientList />
              </Suspense>
            }
          />
        </Route>
      </Route>

      {/* Error Routes */}
      <Route path='/unauthorized' element={<UnauthorizedPage />} />
      <Route path='*' element={<NotFoundPage />} />
    </Routes>
  );
};

export default RoutesComponent;
